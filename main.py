# 在文件开头添加导入
import pyperclip
import tkinter as tk
from tkinter import filedialog, messagebox, ttk, simpledialog
import os
from datetime import datetime
import re
from pydub import AudioSegment
from pydub.utils import make_chunks
from datetime import timedelta
import soundfile as sf
from pymediainfo import MediaInfo
from difflib import SequenceMatcher
from queue import Queue
from pathlib import Path
import math
import threading
import subprocess
import sys
import ctypes
import platform
from chardet import detect
import webview
import time
import json
import traceback
import logging
import importlib.util


# 添加Windows下的startupinfo配置函数
def get_subprocess_startupinfo():
    """为Windows环境返回隐藏控制台窗口的startupinfo"""
    if platform.system() == "Windows":
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        return startupinfo
    return None

# 添加首次启动检测函数
def is_first_run():
    """检查是否是程序首次运行"""
    first_run_flag_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.first_run_completed')
    if not os.path.exists(first_run_flag_file):
        # 创建标记文件以便下次不再显示首次运行界面
        try:
            with open(first_run_flag_file, 'w') as f:
                f.write(str(datetime.now()))
            return True
        except:
            return True  # 如果无法写入文件，仍然视为首次运行
    return False

# 添加首次启动弹窗类
class FirstRunDialog:
    def __init__(self, parent, countdown_seconds=10):
        self.parent = parent
        self.countdown_seconds = countdown_seconds
        self.result = False
        
        # 创建弹窗 - 增大尺寸
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("软件提示")
        self.dialog.geometry("500x350")  # 增大尺寸
        self.dialog.transient(parent)
        self.dialog.grab_set()  # 模态窗口
        
        # 阻止用户通过点击X关闭窗口
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close_attempt)
        
        # 居中显示
        self.center_window()
        
        # 设置图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'BBB.ico')
            if os.path.exists(icon_path):
                self.dialog.iconbitmap(icon_path)
        except Exception as e:
            print(f"无法设置图标: {str(e)}")
        
        # 创建内容
        frame = ttk.Frame(self.dialog, padding=25)  # 增大内边距
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 欢迎标题
        title_label = ttk.Label(frame, text="文运趟音软件提示", 
                             font=("Microsoft YaHei", 16, "bold"))  # 增大字体
        title_label.pack(pady=(0, 25))
        
        # 价格信息 - 使用红色标注价格
        price_frame = ttk.Frame(frame)
        price_frame.pack(fill=tk.X, pady=15)
        
        price_label = ttk.Label(price_frame, text="本软件官方售价：", 
                               font=("Microsoft YaHei", 14))  # 增大字体
        price_label.pack(side=tk.LEFT)
        
        # 创建红色价格标签
        price_value = tk.Label(price_frame, text="499元", 
                             font=("Microsoft YaHei", 16, "bold"),  # 增大字体
                             fg="red",
                             bg=self.dialog.cget("background"))  # 匹配背景色
        price_value.pack(side=tk.LEFT)
        
        # 防盗版提示
        warning_label = ttk.Label(frame, text="谨防受骗！", 
                               font=("Microsoft YaHei", 14, "bold"))  # 增大字体
        warning_label.pack(pady=15)
        
        # 倒计时标签
        self.time_var = tk.StringVar(value=f"请阅读提示信息（{self.countdown_seconds}秒）")
        time_label = ttk.Label(frame, textvariable=self.time_var, 
                            font=("Microsoft YaHei", 12))  # 增大字体
        time_label.pack(pady=15)
        
        # 确认按钮 - 增大按钮
        self.confirm_btn = ttk.Button(frame, text="我已了解（请等待）", 
                                    command=self.on_confirm,
                                    state="disabled")
        self.confirm_btn.pack(pady=15)
        
        # 启动倒计时
        self.update_countdown()
    
    def center_window(self):
        """将窗口居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.dialog.geometry(f"+{x}+{y}")
    
    def update_countdown(self):
        """更新倒计时"""
        if self.countdown_seconds > 0:
            self.time_var.set(f"请阅读提示信息（{self.countdown_seconds}秒）")
            self.countdown_seconds -= 1
            self.dialog.after(1000, self.update_countdown)
        else:
            self.time_var.set("感谢您的耐心阅读")
            self.confirm_btn.config(text="我已了解", state="normal")
    
    def on_confirm(self):
        """确认按钮点击事件"""
        self.result = True
        self.dialog.destroy()
    
    def on_close_attempt(self):
        """尝试关闭窗口时的处理"""
        # 不做任何事情，阻止窗口关闭
        messagebox.showinfo("提示", "请阅读提示信息并点击确认按钮", parent=self.dialog)
    
    def show(self):
        """显示弹窗并等待结果"""
        self.parent.wait_window(self.dialog)
        return self.result

class Logger:
    def __init__(self, logging_level=logging.INFO):
        self.level = logging_level
        # 配置日志格式
        logging.basicConfig(
            level=logging_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
    def info(self, message):
        logging.info(message)
        
    def warning(self, message):
        logging.warning(message)
        
    def error(self, message):
        logging.error(message)
        
    def debug(self, message):
        logging.debug(message)


# 添加当前目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入授权管理模块
try:
    from license_utils import NetworkLicenseManager as LicenseManager
except ImportError:
    # 如果导入失败，尝试从当前目录导入
    license_utils_path = os.path.join(current_dir, 'license_utils.py')
    if os.path.exists(license_utils_path):
        import importlib.util
        spec = importlib.util.spec_from_file_location("license_utils", license_utils_path)
        if spec is not None:  # 添加空值检查
            license_utils = importlib.util.module_from_spec(spec)
            if spec.loader is not None:  # 添加空值检查
                spec.loader.exec_module(license_utils)
                LicenseManager = license_utils.NetworkLicenseManager
            else:
                raise ImportError("无法加载license_utils模块：loader为空")
        else:
            raise ImportError("无法创建license_utils模块规范")
    else:
        raise ImportError("找不到 license_utils.py 文件")

def get_ffmpeg_path():
    """获取 ffmpeg 可执行文件的路径"""
    try:
        # 可能的 ffmpeg 路径列表
        possible_paths = []
        
        # 1. 检查打包资源目录
        if getattr(sys, 'frozen', False):
            # 安全地获取MEIPASS属性
            base_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
            possible_paths.append(base_path)
        
        # 2. 检查当前脚本目录
        script_path = os.path.dirname(os.path.abspath(__file__))
        possible_paths.append(script_path)
        
        # 3. 检查环境变量中的路径
        if 'PATH' in os.environ:
            possible_paths.extend(os.environ['PATH'].split(os.pathsep))
        
        # 4. 检查常见安装路径
        program_files = [
            os.environ.get('ProgramFiles', 'C:\\Program Files'),
            os.environ.get('ProgramFiles(x86)', 'C:\\Program Files (x86)'),
            'E:\\Program Files',
            'D:\\Program Files'
        ]
        
        for pf in program_files:
            possible_paths.append(os.path.join(pf, 'ffmpeg', 'bin'))
        
        # 在所有可能的路径中查找 ffmpeg
        for path in possible_paths:
            ffmpeg_exe = os.path.join(path, 'ffmpeg.exe')
            ffprobe_exe = os.path.join(path, 'ffprobe.exe')
            
            if os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe):
                # 将找到的 ffmpeg 路径添加到环境变量
                os.environ['PATH'] = path + os.pathsep + os.environ.get('PATH', '')
                return ffmpeg_exe  # 返回ffmpeg可执行文件的路径
        
        # 如果找不到 ffmpeg，尝试使用命令行查找
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True)
            return 'ffmpeg'  # 返回命令名称，因为ffmpeg在PATH中
        except:
            pass
        
        return None  # 如果找不到ffmpeg，返回None
    except Exception as e:
        logging.error(f"查找 ffmpeg 时出错：{str(e)}")  # 使用logging替代print
        return None

class TextSelector:
    def __init__(self):
        # 初始化授权管理器
        self.license_manager = LicenseManager()
        
        # 初始化日志管理器
        self.logger = Logger(logging.INFO)
        
        # 创建主窗口
        self.window = tk.Tk()
        self.window.title("文运趟音V2.41正式版 - 官方售价499元")

        # 设置窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'BBB.ico')
            if os.path.exists(icon_path):
                self.window.iconbitmap(icon_path)
        except Exception as e:
            self.logger.warning(f"加载图标失败: {str(e)}")

        # 进程和线程管理
        self.subprocesses = []
        self.threads = []
        self.running = True
        self.video_processor_count = 0
        self.xiaowotool_count = 0
        self.video_editor_count = 0 

        # --- DPI 和尺寸设置 ---
        # 加强DPI适配
        try:
            # 更可靠的DPI获取方法 - 使用系统API
            self.dpi_awareness = ctypes.windll.shcore.GetProcessDpiAwareness(0)
            self.scale_factor = ctypes.windll.shcore.GetScaleFactorForDevice(0) / 100.0
            
            # 如果DPI设置不一致，重新设置DPI感知
            if self.dpi_awareness != 1:  # 1表示系统感知
                ctypes.windll.shcore.SetProcessDpiAwareness(1)
        except:
            try:
                # 旧版Windows
                ctypes.windll.user32.SetProcessDPIAware()
                dc = ctypes.windll.user32.GetDC(0)
                dpi = ctypes.windll.gdi32.GetDeviceCaps(dc, 88)  # LOGPIXELSX
                ctypes.windll.user32.ReleaseDC(0, dc)
                self.scale_factor = dpi / 96.0
            except:
                # 后备方案
                self.scale_factor = self.window.winfo_screenwidth() / 1920.0
        
        # 确保scale_factor至少为1.0，避免UI元素过小
        self.scale_factor = max(1.0, self.scale_factor)
        
        # 调整所有尺寸根据缩放因子
        def scale(size):
            return int(size * self.scale_factor)
        
        # 字体大小定义
        self.base_font_size = scale(10)
        self.small_font_size = scale(9)
        self.large_font_size = scale(14)
        self.button_font_size = scale(10)
        
        # 窗口尺寸和位置
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # 动态计算窗口大小，确保高DPI下窗口不会太大
        window_width = min(int(screen_width * 0.5), int(1200 * self.scale_factor))
        window_height = min(int(screen_height * 0.85), int(900 * self.scale_factor))
        
        # 设定最小窗口尺寸
        self.min_width_for_cards = scale(600) # 用于控制卡片的最小宽度
        min_height = scale(700)
        self.window.minsize(self.min_width_for_cards, min_height)
        
        # 窗口居中
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # --- 颜色定义 ---
        # 定义UI颜色
        self.colors = {
            "bg": "#FFFFFF",  # 纯白色背景
            "fg": "#333333",  # 深灰色文字，比纯黑更柔和
            "card_bg": "#FFFFFF",  # 纯白色卡片背景
            "accent": "#4285F4",  # 更柔和的蓝色
            "button_bg": "#F8F8F8",  # 浅灰色按钮背景
            "button_hover": "#E8E8E8",  # 按钮悬停色
            "button_active": "#4285F4",  # 按钮激活色
            "border": "#DDDDDD",  # 浅灰色边框
            "input_bg": "#FFFFFF",  # 白色输入框背景
            "error": "#D32F2F",  # 柔和的红色
            "success": "#388E3C",  # 柔和的绿色
            "warning": "#F57C00",  # 柔和的橙色
            "description": "#666666",  # 灰色描述文字
            "card_title": "#4285F4"  # 卡片标题颜色
        }
        
        # 获取颜色的辅助函数
        def get_color(name):
            return self.colors.get(name, "#000000")
        
        # 全局字体
        self.DEFAULT_FONT = ("Microsoft YaHei", self.base_font_size)
        self.SMALL_FONT = ("Microsoft YaHei", self.small_font_size)
        self.LARGE_FONT = ("Microsoft YaHei", self.large_font_size, "bold")
        self.BUTTON_FONT = ("Microsoft YaHei", self.button_font_size)
        self.CARD_TITLE_FONT = ("Microsoft YaHei", int(self.base_font_size * 1.1), "bold")
        
        # 配置窗口背景
        self.window.configure(bg=get_color("bg"))
        
        # --- 样式设置 ---
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 应用样式
        self._apply_styles()

        # --- 整体布局 ---
        # 顶部栏框架
        top_bar_frame = ttk.Frame(self.window, style='TopBar.TFrame', height=scale(50))
        top_bar_frame.pack(side=tk.TOP, fill=tk.X, padx=scale(10), pady=(scale(10), 0))
        top_bar_frame.pack_propagate(False)  # 防止子组件改变其大小

        # 程序标题
        app_title_label = ttk.Label(top_bar_frame, text="文运趟音 V2.41正式版", style='Title.TLabel')
        app_title_label.pack(side=tk.LEFT, padx=(scale(10), 0), pady=scale(5))

        # 顶部按钮区域 - 现在包含主题切换和使用教程
        top_buttons_frame = ttk.Frame(top_bar_frame, style='TopBar.TFrame')
        top_buttons_frame.pack(side=tk.RIGHT, padx=scale(10), pady=scale(5))
        
        # 删除了主题切换按钮
        
        # 使用教程按钮
        doc_link_btn = ttk.Button(
            top_buttons_frame,
            text="使用教程",
            command=lambda: self.open_doc_link("https://lcnapqpjgtos.feishu.cn/wiki/JBBEwkjJPi9NYikY96QcsTmKn8b?from=from_copylink"),
            style='TButton'
        )
        doc_link_btn.pack(side=tk.RIGHT, padx=scale(5))

        # 添加大大的价格提示banner
        banner_frame = ttk.Frame(self.window, style='TopBar.TFrame', height=scale(60))
        banner_frame.pack(side=tk.TOP, fill=tk.X, padx=scale(10), pady=(scale(5), scale(5)))
        
        # 使用普通的Canvas创建一个带边框的区域
        banner_canvas = tk.Canvas(
            banner_frame, 
            bg=get_color("bg"),
            height=scale(50),
            highlightthickness=1,
            highlightbackground="red"
        )
        banner_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 在canvas上添加文字
        banner_canvas.create_text(
            banner_canvas.winfo_reqwidth() // 2,  # 水平居中
            scale(25),  # 垂直居中
            text="文运趟音官方售价 499元，谨防受骗！",
            font=("Microsoft YaHei", int(16 * self.scale_factor), "bold"),
            fill="red"
        )

        # 添加免责声明
        disclaimer_label = tk.Label(
            banner_frame,
            text="本软件仅供学习交流，严禁用于非法用途，产生的一切法律责任与作者无关",
            fg="gray",
            bg="#f0f0f0",
            font=("微软雅黑", 9)
        )
        disclaimer_label.pack(fill='x', pady=(0, 5))

        # 主内容框架 (包含画布和滚动条)
        main_content_area = ttk.Frame(self.window, style='Main.TFrame')
        main_content_area.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=scale(10), pady=scale(10))

        # 创建画布
        self.canvas = tk.Canvas(main_content_area, bg=get_color("bg"), highlightthickness=0)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建滚动条
        scrollbar = ttk.Scrollbar(main_content_area, orient=tk.VERTICAL, command=self.canvas.yview, style="Vertical.TScrollbar")
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 创建一个放置所有卡片的框架 (在画布内部)
        self.scrollable_frame = ttk.Frame(self.canvas, style='Main.TFrame')
        self.canvas_frame_id = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        # 当画布或可滚动框架大小改变时，更新滚动区域
        def _on_configure(event):
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            self.canvas.itemconfig(self.canvas_frame_id, width=event.width)
            
            # 更新banner文字位置
            banner_canvas.coords(
                1,  # 假设这是第一个创建的文本项的ID
                banner_canvas.winfo_width() // 2,
                scale(25)
            )
        
        self.scrollable_frame.bind("<Configure>", _on_configure)
        self.canvas.bind('<Configure>', _on_configure)
        banner_canvas.bind('<Configure>', lambda e: banner_canvas.coords(
            1,  # 假设这是第一个创建的文本项的ID
            banner_canvas.winfo_width() // 2,
            scale(25)
        ))

        # 鼠标滚轮滚动
        def _on_mousewheel(event):
            # 根据操作系统调整滚动方向和幅度
            if platform.system() == "Windows":
                self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
            elif platform.system() == "Darwin":  # macOS
                self.canvas.yview_scroll(int(-1 * event.delta), "units")
            else:  # Linux等
                if event.num == 4:
                    self.canvas.yview_scroll(-1, "units")
                elif event.num == 5:
                    self.canvas.yview_scroll(1, "units")
        
        self.canvas.bind_all("<MouseWheel>", _on_mousewheel)
        self.scrollable_frame.bind_all("<MouseWheel>", _on_mousewheel)
        # --- 卡片区域 ---
        # 创建卡片的辅助函数
        def create_card(parent_frame, title, description=""):
            card = ttk.Frame(parent_frame, style='Card.TFrame', relief="solid", borderwidth=1)
            card.pack(fill=tk.X, padx=scale(10), pady=scale(10))
            
            # 尝试应用圆角效果
            card.bind("<Map>", lambda e: self._apply_card_border_radius(card))

            title_label = ttk.Label(card, text=title, style='CardTitle.TLabel')
            title_label.pack(fill=tk.X, padx=scale(10), pady=(scale(5), 0))

            if description:
                desc_label = ttk.Label(card, text=description, style='Description.TLabel', 
                                     wraplength=self.min_width_for_cards * 0.8)
                desc_label.pack(fill=tk.X, padx=scale(10), pady=(0, scale(5)))
            
            content_frame = ttk.Frame(card, style='Card.TFrame')
            content_frame.pack(fill=tk.X, expand=True, padx=scale(10), pady=scale(10))
            return content_frame

        # 1. 授权状态卡片
        license_content = create_card(self.scrollable_frame, "授权状态", "查看当前软件的授权信息和机器码。")
        
        self.license_status_label_widget = ttk.Label(license_content, text="未授权", foreground=get_color("error"), style='TLabel')
        self.license_status_label_widget.pack(side=tk.LEFT, padx=scale(5), pady=scale(5))
        
        self.machine_code = self.license_manager.get_machine_code()
        machine_code_label = ttk.Label(license_content, text=f"机器码: {self.machine_code}", style='TLabel')
        machine_code_label.pack(side=tk.LEFT, padx=scale(5), pady=scale(5))
        
        activate_btn = ttk.Button(license_content,
                               text="激活/管理",
                               command=self.activate_license,
                               name='activate_button',
                               style='Accent.TButton')
        activate_btn.pack(side=tk.RIGHT, padx=scale(5), pady=scale(5))

        # 2. 文件操作卡片
        file_ops_frame = self._create_card(self.scrollable_frame, "文件处理中心", "选择、编辑、备份和恢复您的文本文件。")
        file_ops_frame.pack(fill=tk.X, padx=10, pady=5, anchor='n')
        
        # 文件操作按钮
        buttons_frame = ttk.Frame(file_ops_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        
        # 使用网格布局来均匀分布按钮
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)
        buttons_frame.columnconfigure(2, weight=1)
        buttons_frame.columnconfigure(3, weight=1)
        
        buttons_file_ops = [
            ("选择文本", self.select_file, "选择一个文本文件进行处理。"),
            ("文本编辑", self.open_text_editor, "打开内置编辑器修改当前文本。"),
            ("备份文件", self.backup_file, "为当前文件创建一个安全的备份。"),
            ("恢复备份", self.restore_backup, "从之前的备份恢复文件内容。")
        ]
        
        # 创建按钮并应用新的样式
        for i, (text, cmd, desc) in enumerate(buttons_file_ops):
            button_frame = ttk.Frame(buttons_frame)
            button_frame.grid(row=0, column=i, padx=5, pady=5, sticky="nsew")
            
            # 使用新的按钮创建方法
            btn = self._create_button(button_frame, text, cmd, sticky=True)
            
            # 保存文本编辑按钮的引用
            if text == "文本编辑":
                self.editor_btn = btn
                btn.config(state='disabled')  # 初始禁用
            
            # 添加按钮描述
            desc_label = ttk.Label(button_frame, text=desc, style='Description.TLabel', 
                                wraplength=int(self.min_width_for_cards/5))
            desc_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # 3. 文本智能处理卡片
        text_proc_content = create_card(self.scrollable_frame, "文本智能优化", "一键清理广告、格式化文本、撤销剪切及自定义分段。")
        text_proc_grid = ttk.Frame(text_proc_content, style='Card.TFrame')
        text_proc_grid.pack(fill=tk.X, expand=True)

        # 新增: 智能清理广告按钮 (左侧)
        clean_ads_container = ttk.Frame(text_proc_grid, style='Card.TFrame')
        clean_ads_container.grid(row=0, column=0, padx=scale(5), pady=scale(5), sticky="nsew")
        clean_ads_btn = ttk.Button(clean_ads_container, text="智能清理广告", command=self.clean_ads, style='TButton')
        clean_ads_btn.pack(fill=tk.BOTH, expand=True, ipady=scale(5))
        clean_ads_desc = ttk.Label(clean_ads_container, text="此功能能去除大部分广告，格式化前记得再次检查。",
                                      style='Description.TLabel')
        clean_ads_desc.pack(fill=tk.X, pady=(scale(2), 0))

        # 智能格式化文本按钮 (右侧)
        format_btn_container = ttk.Frame(text_proc_grid, style='Card.TFrame')
        format_btn_container.grid(row=0, column=1, padx=scale(5), pady=scale(5), sticky="nsew")
        format_btn = ttk.Button(format_btn_container, text="智能格式化文本", command=self.format_text, style='Accent.TButton')
        format_btn.pack(fill=tk.BOTH, expand=True, ipady=scale(5))
        format_desc = ttk.Label(format_btn_container, text="去除标点，可选去除章节名，并优化分段。",
                              style='Description.TLabel')
        format_desc.pack(fill=tk.X, pady=(scale(2), 0))
        
        # 撤销剪切按钮
        undo_cut_container = ttk.Frame(text_proc_grid, style='Card.TFrame')
        undo_cut_container.grid(row=1, column=0, padx=scale(5), pady=scale(5), sticky="nsew")
        undo_btn = ttk.Button(undo_cut_container, text="撤销上次剪切", command=self.restore_last_cut, style='TButton')
        undo_btn.pack(fill=tk.BOTH, expand=True, ipady=scale(5))
        undo_desc = ttk.Label(undo_cut_container, text="恢复上一次剪切操作的内容。", 
                            style='Description.TLabel')
        undo_desc.pack(fill=tk.X, pady=(scale(2), 0))

        # 自定义剪切按钮
        custom_cut_container = ttk.Frame(text_proc_grid, style='Card.TFrame')
        custom_cut_container.grid(row=1, column=1, padx=scale(5), pady=scale(5), sticky="nsew")
        
        # 自定义剪切的输入和按钮部分
        custom_cut_input_frame = ttk.Frame(custom_cut_container, style='Card.TFrame')
        custom_cut_input_frame.pack(fill=tk.X)
        
        cut_label = ttk.Label(custom_cut_input_frame, text="剪切字数:", style='TLabel')
        cut_label.pack(side=tk.LEFT, padx=(0, scale(5)))
        
        # 修复: 改进输入框可读性
        self.cut_length_entry = ttk.Entry(custom_cut_input_frame, width=10, 
                                        style='Input.TEntry')
        self.cut_length_entry.pack(side=tk.LEFT, padx=scale(5), fill=tk.X, expand=True)
        self.cut_length_entry.insert(0, "130000")
        
        cut_btn = ttk.Button(custom_cut_input_frame, text="剪切", command=self.custom_cut, 
                           style='TButton', width=8)
        cut_btn.pack(side=tk.LEFT, padx=scale(5))
        
        cut_desc = ttk.Label(custom_cut_container, text="按指定字数剪切文本。", 
                           style='Description.TLabel')
        cut_desc.pack(fill=tk.X, pady=(scale(2), 0))

        # 设置列权重确保均匀分布
        text_proc_grid.columnconfigure(0, weight=1)
        text_proc_grid.columnconfigure(1, weight=1)

        # 4. 音频与配音工坊
        audio_content = create_card(self.scrollable_frame, "音频与配音工坊", "合并、切割音频字幕，使用小蜗工具箱及文本转语音。")
        audio_btn_grid = ttk.Frame(audio_content, style='Card.TFrame')
        audio_btn_grid.pack(fill=tk.X, expand=True)
        
        buttons_audio = [
            ("合并音频与字幕", self.merge_audio, "将多个音频片段和对应字幕合并。"),
            ("切割音频与字幕", self.split_audio, "按段数或时长切割音频和字幕。"),
            ("小蜗工具箱", self.show_xiaowo_tool, "文本分割和音频音量调整。"),
            ("文本转语音", self.show_tts_window, "生成配音和SRT字幕文件。")
        ]
        
        # 优化音频区域布局，确保所有按钮对齐并大小一致
        audio_btn_grid.columnconfigure(0, weight=1)
        audio_btn_grid.columnconfigure(1, weight=1)
        audio_btn_grid.rowconfigure(0, weight=1)
        audio_btn_grid.rowconfigure(1, weight=1)
        
        for i, (text, cmd, desc) in enumerate(buttons_audio):
            col = i % 2
            row = i // 2
            
            # 使用容器确保统一布局
            btn_container = ttk.Frame(audio_btn_grid)
            btn_container.grid(row=row, column=col, padx=scale(5), pady=scale(5), sticky="nsew")
            
            # 按钮填充容器，确保所有按钮大小一致
            btn = ttk.Button(btn_container, text=text, command=cmd, style='TButton')
            btn.pack(fill=tk.BOTH, expand=True, ipady=scale(5))
            
            # 描述标签
            desc_label = ttk.Label(btn_container, text=desc, style='Description.TLabel', 
                                 wraplength=int(self.min_width_for_cards/2*0.8))
            desc_label.pack(fill=tk.X, pady=(scale(2), 0))

        # 5. 视频创作套件
        video_content = create_card(self.scrollable_frame, "视频创作套件", "视频编辑、自动合成、定时关机及素材下载。")
        video_btn_grid = ttk.Frame(video_content, style='Card.TFrame')
        video_btn_grid.pack(fill=tk.X, expand=True)

        buttons_video = [
            ("高级视频编辑", self.show_video_editor, "打开CR VideoMate进行编辑。\n(禁止更新编辑器!)", "error"),
            ("自动合成视频", self.show_video_processor, "快速合成带有配音和字幕的视频。"),
            ("智能定时关机", self.show_auto_shutdown, "检测剪映导出完成后自动关机。\n(此功能为一次性，关闭即退出)", "warning"),
            ("在线素材下载", lambda: self.open_lightweight_browser(), "访问素材网站下载视频。")
        ]
        
        # 优化视频区域布局 - 更新为统一网格布局
        for i, item in enumerate(buttons_video):
            text, cmd = item[0], item[1]
            desc = item[2]
            warning_type = item[3] if len(item) > 3 else None

            # 使用固定的2x2网格布局
            col = i % 2
            row = i // 2
            
            # 使用容器确保统一布局和对齐
            btn_container = ttk.Frame(video_btn_grid)
            btn_container.grid(row=row, column=col, padx=scale(5), pady=scale(5), sticky="nsew")
            
            # 确保按钮的尺寸和对齐一致
            btn = ttk.Button(btn_container, text=text, command=cmd, style='TButton')
            btn.pack(fill=tk.BOTH, expand=True, ipady=scale(5))
            
            # 根据是否有警告使用不同样式，同时确保描述文本换行一致
            if warning_type:
                style_name = f'Warning.{warning_type}.TLabel'
                desc_label = ttk.Label(btn_container, text=desc, style=style_name,
                                    wraplength=int(self.min_width_for_cards/2*0.8))
            else:
                desc_label = ttk.Label(btn_container, text=desc, style='Description.TLabel',
                                    wraplength=int(self.min_width_for_cards/2*0.8))
            desc_label.pack(fill=tk.X, pady=(scale(2), 0))
            
            # 设置列权重
            video_btn_grid.columnconfigure(0, weight=1)
            video_btn_grid.columnconfigure(1, weight=1)
            video_btn_grid.rowconfigure(0, weight=1)
            video_btn_grid.rowconfigure(1, weight=1)

        # --- 底部状态栏 ---
        status_bar_frame = ttk.Frame(self.window, style='StatusBar.TFrame', height=scale(30))
        status_bar_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=scale(10), pady=(0, scale(10)))
        status_bar_frame.pack_propagate(False)
        
        self.file_path_var = tk.StringVar()
        self.path_label = ttk.Label(status_bar_frame,
                                  textvariable=self.file_path_var,
                                  style='Status.TLabel',
                                  wraplength=window_width - scale(20))
        self.path_label.pack(side=tk.LEFT, padx=scale(5), pady=scale(5))
        
        # 初始化变量
        self.text_content = ""
        self.file_path_str = ""
        self.last_cut_text = ""
        self.backup_path = ""
        self.progress_queue = Queue()
        
        # 默认禁用所有功能 (除了激活按钮)
        self.disable_functions()
        
        # 创建主题配置文件，以便其他模块可以读取
        self._create_theme_config()
        
        # 只有首次运行才显示弹窗
        if is_first_run():
            first_run_dialog = FirstRunDialog(self.window)
            first_run_dialog.show()
        
        # 检查授权状态
        if not self.check_license():
            response = messagebox.askquestion("未授权", 
                "软件未激活，是否现在激活？\n" +
                "未激活将无法使用任何功能。", parent=self.window)
            if response == 'yes':
                self.activate_license()
        
        # 检查 ffmpeg
        if not get_ffmpeg_path():
            messagebox.showerror("FFmpeg错误", 
                "找不到 ffmpeg！\n\n" +
                "请确保已安装 ffmpeg 并添加到系统环境变量，\n" +
                "或将 ffmpeg.exe 和 ffprobe.exe 放在程序所在目录。", parent=self.window)
        
        # 设置关闭协议
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 第一次配置滚动区域
        self.window.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def _apply_styles(self):
        """应用样式"""
        # 基础样式
        self.style.configure('.', font=self.DEFAULT_FONT, background=self.colors["bg"], foreground=self.colors["fg"])
        
        # 框架样式
        self.style.configure('Main.TFrame', background=self.colors["bg"])
        self.style.configure('TopBar.TFrame', background=self.colors["bg"])
        self.style.configure('StatusBar.TFrame', background=self.colors["bg"])
        
        # 卡片样式 - 添加圆角和柔和边框
        self.style.configure('Card.TFrame', 
                         background=self.colors["card_bg"], 
                         bordercolor=self.colors["border"],
                         relief="solid",
                         borderwidth=1)
        
        # 标签样式
        self.style.configure('TLabel', background=self.colors["card_bg"], foreground=self.colors["fg"])
        self.style.configure('Title.TLabel', font=self.LARGE_FONT, foreground=self.colors["accent"], background=self.colors["bg"])
        self.style.configure('Description.TLabel', font=self.SMALL_FONT, foreground=self.colors["description"], background=self.colors["card_bg"])
        self.style.configure('CardTitle.TLabel', font=self.CARD_TITLE_FONT, foreground=self.colors["card_title"], background=self.colors["card_bg"])
        self.style.configure('Status.TLabel', font=self.SMALL_FONT, background=self.colors["bg"], foreground=self.colors["description"])
        
        # 按钮样式 - 添加圆角和更现代的外观
        self.style.configure('TButton', 
                        font=self.BUTTON_FONT, 
                        background=self.colors["button_bg"], 
                        foreground=self.colors["fg"],
                        borderwidth=1,
                        focusthickness=0,
                        focuscolor=self.colors["accent"],
                        padding=(10, 5),
                        relief="raised")
        
        # 按钮悬停和激活状态
        self.style.map('TButton',
                  background=[('active', self.colors["button_active"]), ('hover', self.colors["button_hover"])],
                  foreground=[('active', "#FFFFFF")],
                  relief=[('pressed', 'sunken'), ('hover', 'raised')])
        
        # 强调按钮 - 使用主题色
        self.style.configure('Accent.TButton', 
                          background=self.colors["accent"], 
                          foreground="#FFFFFF",
                          relief="raised",
                          padding=(10, 5))
        
        self.style.map('Accent.TButton',
                  background=[('active', self.colors["button_active"]), ('hover', self.colors["button_hover"])],
                  foreground=[('hover', "#FFFFFF"), ('active', "#FFFFFF")])
        
        # 图标按钮
        self.style.configure('Icon.TButton', 
                          font=("Segoe UI Symbol", self.button_font_size),
                          padding=(5, 2))
        
        # 输入框样式 - 改进边框和焦点效果
        self.style.configure('Input.TEntry', 
                         fieldbackground=self.colors["input_bg"], 
                         foreground=self.colors["fg"],
                         insertcolor=self.colors["fg"],
                         bordercolor=self.colors["border"],
                         padding=(5, 2),
                         lightcolor=self.colors["border"],
                         darkcolor=self.colors["border"])
        
        self.style.map('Input.TEntry',
                  bordercolor=[('focus', self.colors["accent"])])
        
        # 滚动条样式 - 更现代的外观
        self.style.configure("Vertical.TScrollbar", 
                         background=self.colors["button_bg"], 
                         troughcolor=self.colors["bg"], 
                         bordercolor=self.colors["border"],
                         arrowcolor=self.colors["fg"],
                         relief="flat",
                         arrowsize=13,
                         width=12)
        
        self.style.map("Vertical.TScrollbar",
            background=[('active', self.colors["button_hover"]), ('pressed', self.colors["button_active"])],
            arrowcolor=[('pressed', self.colors["accent"])])
        
        # 警告标签样式
        self.style.configure('Warning.error.TLabel', 
                          font=self.SMALL_FONT, 
                          foreground=self.colors["error"], 
                          background=self.colors["card_bg"])
        self.style.configure('Warning.warning.TLabel', 
                          font=self.SMALL_FONT, 
                          foreground=self.colors["warning"], 
                          background=self.colors["card_bg"])
        
        # 设置画布背景色
        if hasattr(self, 'canvas'):
            self.canvas.configure(bg=self.colors["bg"])
    
    def _create_theme_config(self):
        """创建主题配置文件，供TTS和视频处理等子工具读取"""
        try:
            # 创建配置字典
            theme_config = {
                "theme": "light",
                "colors": self.colors,
                "base_font_size": self.base_font_size
            }
            
            # 保存配置到文件
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'theme_config.json')
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(theme_config, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"创建主题配置失败: {str(e)}")

    def center_window(self):
        """使窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def on_resize(self, event):
        """窗口大小改变时保持内容居中"""
        if event.widget == self.window:
            self.window.update_idletasks()
    
    def backup_file(self):
        if not self.file_path_str:
            messagebox.showwarning("警告", "请先选择文本文件！")
            return
            
        try:
            # 检测原始文件编码
            with open(self.file_path_str, 'rb') as f:
                rawdata = f.read(4096)  # 只读取前4KB来检测编码
                result = detect(rawdata)
                original_encoding = result['encoding']
            
            # 如果检测失败，尝试常用编码
            if not original_encoding:
                for encoding in ['utf-8', 'gbk', 'gb18030', 'big5', 'shift_jis']:
                    try:
                        with open(self.file_path_str, 'r', encoding=encoding) as f:
                            f.read()
                        original_encoding = encoding
                        break
                    except UnicodeDecodeError:
                        continue
            
            if not original_encoding:
                # 如果还是无法确定编码，提示用户选择
                original_encoding = simpledialog.askstring(
                    "选择编码",
                    "无法自动识别文件编码，请手动输入编码格式：\n"
                    "常用编码：utf-8, gbk, gb18030, big5, shift_jis等",
                    parent=self.window
                )
                if not original_encoding:
                    messagebox.showwarning("警告", "未指定编码，备份已取消")
                    return

            # 读取源文件内容，添加错误处理
            with open(self.file_path_str, 'r', encoding=original_encoding, errors='ignore') as src:
                content = src.read()

            # 创建备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{os.path.splitext(self.file_path_str)[0]}_backup_{timestamp}.txt"
            
            # 使用原始编码保存备份
            with open(backup_name, 'w', encoding=original_encoding) as dst:
                dst.write(content)
                
            self.backup_path = backup_name
            messagebox.showinfo("成功", f"文件已备份为：\n{backup_name}\n编码格式：{original_encoding}")
            
        except Exception as e:
            messagebox.showerror("错误", f"备份失败：{str(e)}")
    
    def restore_backup(self):
        if not self.file_path_str:
            messagebox.showwarning("警告", "请先选择要处理的文本文件！")
            return
            
        backup_path = filedialog.askopenfilename(
            filetypes=[("Text files", "*.txt")],
            initialdir=os.path.dirname(self.file_path_str),
            title="选择要恢复的备份文件"
        )
        
        if backup_path:
            while True:
                confirm = messagebox.askyesno(
                    "确认恢复", 
                    f"确定要恢复到以下备份文件吗？\n{os.path.basename(backup_path)}")
                
                if confirm:
                    try:
                        # 检测备份文件的编码
                        with open(backup_path, 'rb') as f:
                            rawdata = f.read(4096)
                            result = detect(rawdata)
                            backup_encoding = result['encoding']
                        
                        # 如果检测失败，尝试常用编码
                        if not backup_encoding:
                            for encoding in ['utf-8', 'gbk', 'gb18030']:
                                try:
                                    with open(backup_path, 'r', encoding=encoding) as f:
                                        content = f.read()
                                    backup_encoding = encoding
                                    break
                                except UnicodeDecodeError:
                                    continue
                        
                        if not backup_encoding:
                            backup_encoding = simpledialog.askstring(
                                "选择编码",
                                "无法自动识别备份文件编码，请手动输入编码格式：\n"
                                "常用编码：utf-8, gbk, gb18030, big5, shift_jis等",
                                parent=self.window
                            )
                            if not backup_encoding:
                                messagebox.showwarning("警告", "未指定编码，恢复已取消")
                                return
                        
                        # 读取备份文件内容
                        with open(backup_path, 'r', encoding=backup_encoding) as f:
                            content = f.read()
                        
                        # 保存到原始文件，使用相同的编码
                        with open(self.file_path_str, 'w', encoding=backup_encoding) as f:
                            f.write(content)
                        
                        # 更新主窗口中的文本内容
                        self.text_content = content
                        
                        messagebox.showinfo("成功", f"已恢复到所选备份文件！\n使用编码：{backup_encoding}")
                        break
                        
                    except Exception as e:
                        messagebox.showerror("错误", f"恢复失败：{str(e)}")
                        break
                else:
                    backup_path = filedialog.askopenfilename(
                        filetypes=[("Text files", "*.txt")],
                        initialdir=os.path.dirname(self.file_path_str),
                        title="选择要恢复的备份文件"
                    )
                    if not backup_path:
                        break
    
    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            self.file_path_var.set(file_path)
            self.file_path_str = file_path
            try:
                # 只读取文件的前4KB来检测编码，避免大文件导致的卡顿
                with open(file_path, 'rb') as file:
                    raw_data = file.read(4096)
                    detected = detect(raw_data)
                    encoding = detected['encoding']
                
                # 优先使用检测到的编码
                try:
                    with open(file_path, 'r', encoding=encoding or 'utf-8') as file:
                        self.text_content = file.read()
                    messagebox.showinfo("成功", f"文件加载成功！\n使用编码：{encoding}")
                    # 成功读取文件后启用编辑器按钮
                    self.editor_btn.config(state='normal')
                    return
                except UnicodeDecodeError:
                    pass
                
                # 如果检测到的编码失败，只尝试最常用的几种编码
                common_encodings = ['utf-8', 'gbk', 'gb18030']
                for encoding in common_encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as file:
                            self.text_content = file.read()
                        messagebox.showinfo("成功", f"文件加载成功！\n使用编码：{encoding}")
                        # 成功读取文件后启用编辑器按钮
                        self.editor_btn.config(state='normal')
                        return
                    except UnicodeDecodeError:
                        continue
                
                # 如果常用编码都失败，再提示用户手动选择
                chosen_encoding = simpledialog.askstring("选择编码",
                                                       "无法自动识别编码，请手动输入编码格式：\n"
                                                       "常用编码：gbk, gb18030, big5, shift_jis等",
                                                       parent=self.window)
                if chosen_encoding:
                    try:
                        with open(file_path, 'r', encoding=chosen_encoding) as file:
                            self.text_content = file.read()
                        messagebox.showinfo("成功", f"文件加载成功！\n使用编码：{chosen_encoding}")
                        # 成功读取文件后启用编辑器按钮
                        self.editor_btn.config(state='normal')
                    except UnicodeDecodeError:
                        messagebox.showerror("错误", f"使用 {chosen_encoding} 编码无法读取文件")
                else:
                    messagebox.showwarning("警告", "已取消文件加载")

            except Exception as e:
                messagebox.showerror("错误", f"无法读取文件：{str(e)}")
            # 如果读取失败，确保编辑器按钮保持禁用状态
            self.editor_btn.config(state='disabled')

    def restore_last_cut(self):
        if not self.last_cut_text:
            messagebox.showwarning("警告", "没有可撤销的操作！")
            return
            
        try:
            # 创建文本编辑器窗口
            editor = TextEditor(self.window, self.file_path_str, self)
            
            # 等待编辑器窗口完全加载
            self.window.update()
            editor.window.update()
            
            # 获取当前文本内容
            current_text = editor.text_area.get(1.0, tk.END)
            
            # 合并之前剪切的内容和当前内容
            full_text = self.last_cut_text + current_text
            
            # 更新编辑器内容
            editor.text_area.delete(1.0, tk.END)
            editor.text_area.insert(1.0, full_text)
            
            # 自动保存更改
            editor.save_file()
            
            # 更新主窗口中的文本内容
            self.text_content = full_text
            
            # 清除上次剪切的内容
            self.last_cut_text = ""
            
            messagebox.showinfo("成功", "已撤销上次剪切！")
            
            # 关闭编辑器窗口
            editor.window.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"撤销失败：{str(e)}")

    def select_text(self, target_length):
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        if not self.file_path_str:
            messagebox.showwarning("警告", "请先选择文本文件！")
            return
        
        try:
            # 创建文本编辑器窗口
            editor = TextEditor(self.window, self.file_path_str, self)
            
            # 等待编辑器窗口完全加载
            self.window.update()
            editor.window.update()
            
            # 获取文本内容
            text_content = editor.text_area.get(1.0, tk.END)
            current_length = len(text_content.strip())
            
            if current_length < target_length:
                messagebox.showwarning("警告", 
                    f"文本内容不足！\n当前字数：{current_length}\n需要字数：{target_length}")
                editor.window.destroy()
                return
            
            # 保存要剪切的文本
            self.last_cut_text = text_content[:target_length]
            remaining_text = text_content[target_length:]
            
            # 更新编辑器内容
            editor.text_area.delete(1.0, tk.END)
            editor.text_area.insert(1.0, remaining_text)
            
            # 自动保存更改
            editor.save_file()
            
            # 将剪切的内容复制到剪贴板
            pyperclip.copy(self.last_cut_text)
            
            # 更新主窗口中的文本内容
            self.text_content = remaining_text
            
            messagebox.showinfo("成功", 
                f"已剪切{target_length}字并复制到剪贴板！\n剩余字数：{len(remaining_text)}")
            
            # 关闭编辑器窗口
            editor.window.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"操作失败：{str(e)}")

    def is_semantic_break(self, prev_word, curr_word):
        """判断是否语义分割点（来自tts.py）"""
        break_points = {'啊','呢','吗','吧','啦','的','地','得','了','着','过','就','才','都','和','与','及','在','于','从'}
        return prev_word in break_points or curr_word in break_points

    def clean_ads(self):
        """清理广告、PS等无关内容，并提供预览"""
        if not self.file_path_str:
            messagebox.showwarning("警告", "请先选择文本文件！")
            return

        try:
            editor = TextEditor(self.window, self.file_path_str, self)
            self.window.update()
            editor.window.update()
            content = editor.text_area.get(1.0, tk.END)

            # STAGE 1: 清理行内嵌的注释（如【...加更...】或 (PS: ...)）
            inline_keywords = ['ps', '催更', '加更', '打赏', '书友', '番外', '月票', '题外话', 'jpg', 'png', '图片']
            # 构建正则表达式: [开括号]...[关键词]...[闭括号]
            inline_pattern_str = r'[\[【\(（「『《][^\]】\)」』》）]*?(?:' + '|'.join(inline_keywords) + r')[^\]】\)」』》）]*?[\]】\)」』》）]'
            inline_pattern = re.compile(inline_pattern_str, re.IGNORECASE)
            lines = content.split('\n')
            cleaned_lines_stage1 = [inline_pattern.sub('', line) for line in lines]
            content = '\n'.join(cleaned_lines_stage1)

            # STAGE 2: 过滤整行/整块的无关内容
            block_keyword_patterns = [
                r'^\s*ps\d*[\s:：]',               # PS 开头
                r'^\s*[\[【\(（「『《]\s*Ps',     # [PS  或其他括号-PS
                r'^\s*（ps）',
                r'^\s*作者说', r'^\s*作者的话',
                r'^\s*题外话', r'^\s*书友群', r'^\s*催更', r'^\s*加更', r'^\s*打赏',
                r'^\s*书友', r'^\s*番外', r'^\s*月票', r'^\s*标题', r'^\s*简介',
                r'^\s*抱歉', r'^\s*正文前说一下',
                r'^\s*jpg', r'^\s*png', r'^\s*图片',
            ]
            original_lines = content.split('\n')
            flagged_indices, flagged_lines, skip_block = [], [], False

            for idx, line in enumerate(original_lines):
                stripped = line.strip()
                if skip_block:
                    is_blank_line = not stripped
                    # 仅当出现明显新的段落（缩进/章节标题）才结束跳过，避免PS内容被截断
                    is_new_paragraph = (
                        line.startswith(('  ', '　　')) or  # 缩进段落
                        re.match(r'^\s*第\s*([0-9]+|[零一二三四五六七八九十百千]+)\s*章', stripped)  # 章节标题
                    )
                    if is_blank_line or is_new_paragraph:
                        skip_block = False
                        if is_blank_line: continue
                    else:
                        flagged_indices.append(idx)
                        flagged_lines.append(line)
                        continue
                
                if not stripped: continue

                # 宽松规则：若行前 20 个字符内出现 ps / p.s 也视为广告行
                ps_prefix = stripped.lower()[:20]
                has_loose_ps = re.search(r'p\.?s', ps_prefix) is not None

                if has_loose_ps or any(re.search(pat, stripped.lower()) for pat in block_keyword_patterns):
                    flagged_indices.append(idx)
                    flagged_lines.append(line)
                    skip_block = True
                    continue

            if not flagged_lines:
                messagebox.showinfo("提示", "未找到可清理的广告或PS内容。")
                editor.window.destroy()
                return

            preview_win = tk.Toplevel(self.window)
            preview_win.title("预览并选择要删除的无关内容")
            preview_win.geometry("700x440")
            preview_win.transient(self.window)
            preview_win.grab_set()
            # ... (rest of preview window logic is the same)
            info_lbl = ttk.Label(preview_win, text=f"共检测到 {len(flagged_lines)} 行无关内容，请取消选择要保留的行：")
            info_lbl.pack(pady=5)
            list_frame = ttk.Frame(preview_win)
            list_frame.pack(fill=tk.BOTH, expand=True, padx=10)
            y_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL)
            y_scroll.pack(side=tk.RIGHT, fill=tk.Y)
            listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE, yscrollcommand=y_scroll.set, font=("Consolas", 10))
            for line in flagged_lines:
                listbox.insert(tk.END, line)
            listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            y_scroll.config(command=listbox.yview)
            listbox.select_set(0, tk.END)
            btn_frame = ttk.Frame(preview_win)
            btn_frame.pack(pady=10)
            user_selection = {"confirmed": False, "indices": []}
            def on_confirm():
                user_selection["confirmed"] = True
                user_selection["indices"] = listbox.curselection()
                preview_win.destroy()
            def on_cancel():
                preview_win.destroy()
            ttk.Button(btn_frame, text="确认删除", command=on_confirm, width=12).pack(side=tk.LEFT, padx=5)
            ttk.Button(btn_frame, text="取消", command=on_cancel, width=12).pack(side=tk.LEFT, padx=5)
            preview_win.wait_window()

            if not user_selection["confirmed"]:
                editor.window.destroy()
                return

            indices_to_delete = {flagged_indices[i] for i in user_selection["indices"]}
            final_lines = [line for idx, line in enumerate(original_lines) if idx not in indices_to_delete]
            cleaned_content = '\n'.join(final_lines)

            if content != cleaned_content:
                new_file_path = os.path.splitext(self.file_path_str)[0] + "_清理后.txt"
                with open(new_file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                TextEditor(self.window, new_file_path, self)
                messagebox.showinfo("成功", f"清理完成！共删除 {len(indices_to_delete)} 行。\n新文件已保存为：\n{new_file_path}")
            
            editor.window.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"清理失败：{str(e)}")

    def format_text(self):
        """格式化文本，可选删除章节"""
        if not self.file_path_str:
            messagebox.showwarning("警告", "请先选择文本文件！")
            return
            
        try:
            remove_chapters = messagebox.askyesno(
                "格式化选项", 
                "是否删除章节名称？\n(如：第1章、第一章等)"
            )
            
            editor = TextEditor(self.window, self.file_path_str, self)
            self.window.update()
            editor.window.update()
            
            content = editor.text_area.get(1.0, tk.END)

            if remove_chapters:
                lines = content.split('\n')
                lines = [line for line in lines if not re.match(r'^\s*第([0-9]+|[零一二三四五六七八九十百千]+)章', line.strip())]
                content = '\n'.join(lines)
            
            formatted_content = self.auto_format_text(content)
            
            new_file_path = os.path.splitext(self.file_path_str)[0] + "_格式化.txt"
            
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_content)
            
            TextEditor(self.window, new_file_path, self)
            
            total_chars = len(formatted_content.replace('\n', ''))
            messagebox.showinfo("成功", 
                f"格式化完成！\n新文件已保存为：\n{new_file_path}\n总字数：{total_chars}")
            
            editor.window.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"格式化失败：{str(e)}")

    def auto_format_text(self, text):
        """自动格式化文本"""
        paragraphs = text.split('\n')
        processed_paragraphs = []
        
        for para in paragraphs:
            if not para.strip():
                continue
            
            # 处理标点符号
            processed = re.sub(r'([，。！？])', ' ', para)
            # 使用正确的正则表达式
            cleaned = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\+\-\/\%\*\:]', ' ', processed)
            
            # 自然分段处理
            sentences = []
            current_sentence = []
            current_len = 0
            parts = [p.strip() for p in cleaned.split() if p.strip()]
            
            for part in parts:
                part_len = len(part)
                if current_len + part_len > 20:
                    if current_sentence:
                        sentences.append(' '.join(current_sentence))
                        current_sentence = []
                        current_len = 0
                    current_sentence.append(part)
                    current_len = part_len
                elif 20 <= current_len + part_len <= 20:
                    current_sentence.append(part)
                    sentences.append(' '.join(current_sentence))
                    current_sentence = []
                    current_len = 0
                else:
                    if part.isdigit() and current_len > 15:
                        sentences.append(' '.join(current_sentence))
                        current_sentence = [part]
                        current_len = part_len
                    else:
                        current_sentence.append(part)
                        current_len += part_len + 1
            
            if current_sentence:
                if len(''.join(current_sentence)) < 5 and sentences:
                    sentences[-1] += ' ' + ' '.join(current_sentence)
                else:
                    sentences.append(' '.join(current_sentence))
            
            if sentences:
                processed_paragraphs.append('\n'.join(sentences))
        
        # 最终合并处理
        final_lines = []
        for para in processed_paragraphs:
            lines = para.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                if len(line) > 20:
                    chunks = []
                    current_chunk = []
                    current_chunk_len = 0
                    words = line.split()
                    for i, word in enumerate(words):
                        word_len = len(word)
                        if i > 0 and self.is_semantic_break(words[i-1], word):
                            if current_chunk_len > 15:
                                chunks.append(' '.join(current_chunk))
                                current_chunk = []
                                current_chunk_len = 0
                        if current_chunk_len + word_len > 20:
                            chunks.append(' '.join(current_chunk))
                            current_chunk = [word]
                            current_chunk_len = word_len
                        else:
                            current_chunk.append(word)
                            current_chunk_len += word_len + 1
                    if current_chunk:
                        chunks.append(' '.join(current_chunk))
                    final_lines.extend(chunks)
                else:
                    final_lines.append(line)
        
        # 最终清理
        cleaned_lines = []
        for line in final_lines:
            line = re.sub(r'\s+', ' ', line)
            line = re.sub(r'(\d)\s+(?=\d)', r'\1', line)
            line = re.sub(r'\s+([+/-])', r'\1', line)
            line = re.sub(r'([+/-])\s+', r'\1', line)
            cleaned_lines.append(line.strip())
        
        return '\n'.join(cleaned_lines)

    def custom_cut(self):
        """处理自定义剪切"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        try:
            length = int(self.cut_length_entry.get())
            if length <= 0:
                messagebox.showwarning("警告", "请输入大于0的数字！")
                return
            self.select_text(length)
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的数字！")

    def merge_audio(self):
        """合并音频文件和字幕文件"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        # 选择文件夹
        folder_path = filedialog.askdirectory(title="选择包含音频和字幕文件的文件夹")
        if not folder_path:
            return
        
        try:
            # 获取所有mp3文件并按文件名排序
            audio_files = []
            for file in os.listdir(folder_path):
                if file.endswith('.mp3'):
                    audio_files.append(os.path.join(folder_path, file))
            
            # 按文件名序号排序
            audio_files.sort(key=lambda x: int(os.path.basename(x).split('_')[0]))
            
            if not audio_files:
                raise Exception("没有找到音频文件")
                
            # 查找对应的字幕文件
            subtitle_files = []
            has_subtitles = False
            for file in os.listdir(folder_path):
                if file.endswith('.srt'):
                    subtitle_files.append(os.path.join(folder_path, file))
                    has_subtitles = True
            
            # 按文件名序号排序字幕文件（如果有）
            if has_subtitles:
                subtitle_files.sort(key=lambda x: int(os.path.basename(x).split('_')[0]))
                
            # 创建进度窗口
            progress_window = tk.Toplevel(self.window)
            progress_window.title("合并进度")
            progress_window.geometry("400x150")
            progress_window.transient(self.window)
            
            # 设置进度窗口在主窗口中间
            x = self.window.winfo_x() + (self.window.winfo_width() - 400) // 2
            y = self.window.winfo_y() + (self.window.winfo_height() - 150) // 2
            progress_window.geometry(f"+{x}+{y}")
            
            # 创建进度标签和进度条
            status_label = tk.Label(progress_window, text="正在合并...", wraplength=350)
            status_label.pack(pady=10)
            
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                progress_window, 
                variable=progress_var,
                maximum=100,
                length=350,
                mode='determinate'
            )
            progress_bar.pack(pady=10)
            
            # 处理音频合并
            def process_merge():
                try:
                    # 更新状态
                    status_label.config(text="正在准备音频文件列表...")
                    progress_var.set(10)
                    progress_window.update()
                    
                    # 获取ffmpeg路径
                    ffmpeg_path = get_ffmpeg_path()
                    if not ffmpeg_path:
                        raise Exception("未找到FFmpeg，无法执行合并操作")
                    
                    # 创建文件列表
                    filelist_path = os.path.join(folder_path, "filelist.txt")
                    with open(filelist_path, 'w', encoding='utf-8') as f:
                        for audio_file in audio_files:
                            # 确保路径格式正确
                            safe_path = audio_file.replace('\\', '/')
                            f.write(f"file '{safe_path}'\n")
                    
                    # 更新状态
                    status_label.config(text="快速合并音频中...")
                    progress_var.set(30)
                    progress_window.update()
                    
                    # 合并后的音频文件路径
                    output_audio_file = os.path.join(folder_path, "合并音频.mp3")
                    
                    # 构建FFmpeg命令
                    cmd = [
                        str(ffmpeg_path),
                        '-y',  # 覆盖输出文件
                        '-f', 'concat',  # 使用concat格式
                        '-safe', '0',  # 允许使用绝对路径
                        '-i', filelist_path,  # 输入文件列表
                        '-c', 'copy',  # 直接复制流，不重新编码
                        output_audio_file
                    ]
                    
                    # 在Windows上添加创建无窗口进程的标记
                    creation_flags = 0
                    if platform.system() == 'Windows':
                        creation_flags = subprocess.CREATE_NO_WINDOW
                    
                    # 执行FFmpeg命令
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        creationflags=creation_flags
                    )
                    
                    # 等待进程完成
                    stdout_data, stderr_data = process.communicate()
                    
                    # 尝试解码输出
                    try:
                        stderr = stderr_data.decode('utf-8', errors='replace')
                    except:
                        stderr = str(stderr_data)
                    
                    # 删除临时文件列表
                    if os.path.exists(filelist_path):
                        try:
                            os.remove(filelist_path)
                        except:
                            pass  # 忽略删除临时文件的错误
                    
                    # 检查处理结果
                    if process.returncode != 0:
                        print(f"FFmpeg错误: {stderr}")
                        raise Exception(f"音频合并失败: {stderr}")
                    
                    # 验证音频输出文件
                    if not os.path.exists(output_audio_file):
                        raise Exception("音频合并失败：未生成输出文件")
                    if os.path.getsize(output_audio_file) == 0:
                        raise Exception("音频合并失败：输出文件为空")
                    
                    progress_var.set(50)
                    
                    # 处理字幕合并（如果有）
                    success_message = f"音频合并完成！\n保存至：{output_audio_file}"
                    
                    if has_subtitles:
                        status_label.config(text="正在合并字幕文件...")
                        progress_var.set(60)
                        progress_window.update()
                        
                        # 使用TTS中经过验证的字幕合并代码
                        try:
                            # 导入pysrt库（如果没有安装会报错）
                            import pysrt
                        except ImportError:
                            messagebox.showerror("错误", "缺少pysrt库！\n请运行: pip install pysrt")
                            return

                        # 创建最终字幕文件
                        final_subs = pysrt.SubRipFile()

                        # 字幕索引计数器
                        subtitle_index = 1

                        # 当前偏移时间（秒）
                        current_offset = 0.0
                        
                        status_label.config(text="正在处理字幕文件...")
                        progress_window.update()

                        # 处理每个字幕文件
                        for i, (srt_file, audio_file) in enumerate(zip(subtitle_files, audio_files)):
                            status_label.config(text=f"正在处理字幕文件 {i+1}/{len(subtitle_files)}...")
                            progress = 60 + (i / len(subtitle_files)) * 30
                            progress_var.set(progress)
                            progress_window.update()

                            print(f"处理第 {i+1}/{len(subtitle_files)} 个字幕文件")

                            # 跳过不存在的文件
                            if not os.path.exists(srt_file):
                                print(f"字幕文件不存在，跳过: {srt_file}")
                                continue

                            if not os.path.exists(audio_file):
                                print(f"音频文件不存在，跳过: {audio_file}")
                                continue

                            # 获取音频文件精确时长
                            try:
                                # 使用FFmpeg获取音频时长（秒）
                                command = [
                                    str(ffmpeg_path),
                                    "-v", "error",
                                    "-show_entries", "format=duration",
                                    "-of", "default=noprint_wrappers=1:nokey=1",
                                    audio_file
                                ]

                                # 创建进程
                                process = subprocess.run(
                                    command,
                                    capture_output=True,
                                    text=True,
                                    creationflags=creation_flags if platform.system() == 'Windows' else 0
                                )

                                # 检查是否成功
                                if process.returncode != 0:
                                    print(f"获取音频时长失败: {process.stderr}")
                                    continue

                                # 解析输出获取时长
                                audio_duration = float(process.stdout.strip())

                                if audio_duration <= 0:
                                    print(f"警告: 音频时长无效 ({audio_duration}秒)")
                                    continue

                                print(f"音频文件时长: {audio_duration:.3f}秒")

                            except Exception as e:
                                print(f"获取音频时长出错: {str(e)}")
                                continue

                            # 读取字幕文件
                            try:
                                current_subs = pysrt.open(srt_file, encoding='utf-8')
                                print(f"成功读取字幕文件，包含 {len(current_subs)} 条字幕")
                            except Exception as e:
                                print(f"读取字幕文件失败: {str(e)}")
                                continue

                            # 如果字幕文件为空，跳过
                            if len(current_subs) == 0:
                                print(f"字幕文件为空，跳过")
                                continue

                            # 将当前字幕添加到最终字幕列表，调整时间戳
                            for sub in current_subs:
                                # 创建新字幕
                                new_sub = pysrt.SubRipItem()
                                new_sub.index = subtitle_index
                                subtitle_index += 1

                                # 复制文本
                                new_sub.text = sub.text

                                # 计算原始时间（秒）
                                orig_start_sec = sub.start.hours * 3600 + sub.start.minutes * 60 + sub.start.seconds + sub.start.milliseconds / 1000.0
                                orig_end_sec = sub.end.hours * 3600 + sub.end.minutes * 60 + sub.end.seconds + sub.end.milliseconds / 1000.0

                                # 调整时间戳比例，确保适配音频文件实际时长
                                # 计算字幕文件中的最大时间戳
                                max_subtitle_time = 0
                                for s in current_subs:
                                    end_time = s.end.hours * 3600 + s.end.minutes * 60 + s.end.seconds + s.end.milliseconds / 1000.0
                                    max_subtitle_time = max(max_subtitle_time, end_time)

                                # 如果字幕总时长与音频时长有显著差异，调整比例
                                if max_subtitle_time > 0 and abs(max_subtitle_time - audio_duration) > 0.5:
                                    time_scale = audio_duration / max_subtitle_time
                                    orig_start_sec *= time_scale
                                    orig_end_sec *= time_scale
                                    print(f"调整字幕时间比例: {time_scale:.3f}")

                                # 添加当前偏移
                                new_start_sec = orig_start_sec + current_offset
                                new_end_sec = orig_end_sec + current_offset

                                # 设置新的开始时间
                                new_sub.start.hours = int(new_start_sec // 3600)
                                new_sub.start.minutes = int((new_start_sec % 3600) // 60)
                                new_sub.start.seconds = int(new_start_sec % 60)
                                new_sub.start.milliseconds = int((new_start_sec - int(new_start_sec)) * 1000)

                                # 设置新的结束时间
                                new_sub.end.hours = int(new_end_sec // 3600)
                                new_sub.end.minutes = int((new_end_sec % 3600) // 60)
                                new_sub.end.seconds = int(new_end_sec % 60)
                                new_sub.end.milliseconds = int((new_end_sec - int(new_end_sec)) * 1000)

                                # 添加到最终字幕列表
                                final_subs.append(new_sub)

                            # 更新时间偏移，使用音频的实际时长
                            current_offset += audio_duration
                            print(f"当前总偏移: {current_offset:.3f}秒")

                        # 保存合并后的字幕文件
                        status_label.config(text="正在生成合并后的字幕文件...")
                        progress_var.set(90)
                        progress_window.update()

                        merged_srt_path = os.path.join(folder_path, "合并字幕.srt")
                        final_subs.save(merged_srt_path, encoding='utf-8')
                        print(f"字幕合并完成，共 {len(final_subs)} 条，总时长 {current_offset:.2f}秒")
                        
                        success_message += f"\\n字幕也已合并！\\n保存至：{merged_srt_path}"
                    
                    # 设置进度为100%
                    progress_var.set(100)
                    status_label.config(text="处理完成！")
                    progress_window.update()
                    
                    # 延迟关闭窗口
                    progress_window.after(1000, progress_window.destroy)
                    messagebox.showinfo("成功", success_message)
                    
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("错误", f"合并失败：{str(e)}")
            
            # 创建并启动处理线程
            thread = threading.Thread(target=process_merge)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"合并初始化失败：{str(e)}")

    def split_audio(self):
        """将音频文件和字幕文件切割成多段"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        # 选择音频文件
        audio_file = filedialog.askopenfilename(
            filetypes=[("音频文件", "*.wav;*.mp3;*.m4a")],
            title="选择要切割的音频文件"
        )
        if not audio_file:
            return
            
        # 选择字幕文件（可选）
        subtitle_file = None
        if messagebox.askyesno("字幕文件", "是否要同时处理字幕文件？"):
            subtitle_file = filedialog.askopenfilename(
                filetypes=[("SRT字幕文件", "*.srt")],
                title="选择要切割的字幕文件"
            )
            
        try:
            # 使用ffprobe获取音频时长
            ffmpeg_path = get_ffmpeg_path()
            if not ffmpeg_path:
                messagebox.showerror("错误", "未找到ffmpeg，请先安装ffmpeg或将其添加到系统路径！")
                return
                
            ffprobe_path = ffmpeg_path.replace('ffmpeg.exe', 'ffprobe.exe') if ffmpeg_path else None
            if not ffprobe_path or not os.path.exists(ffprobe_path):
                messagebox.showerror("错误", "未找到ffprobe，请先安装ffmpeg或将其添加到系统路径！")
                return
                
            cmd = [
                str(ffprobe_path), 
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                str(audio_file)
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                messagebox.showerror("错误", "无法获取音频时长")
                return
            
            # 获取总时长（秒）
            duration_seconds = float(result.stdout.strip())
            duration_minutes = duration_seconds / 60
            duration_hours = duration_seconds / 3600
            
            # 创建自定义切割选项对话框
            split_dialog = tk.Toplevel(self.window)
            split_dialog.title("音频和字幕切割选项")
            split_dialog.geometry("650x500")
            split_dialog.transient(self.window)
            split_dialog.grab_set()

            # 设置对话框在主窗口中间
            x = self.window.winfo_x() + (self.window.winfo_width() - 400) // 2
            y = self.window.winfo_y() + (self.window.winfo_height() - 350) // 2
            split_dialog.geometry(f"+{x}+{y}")
            
            # 音频信息
            info_frame = ttk.Frame(split_dialog, padding=10)
            info_frame.pack(fill=tk.X)
            
            # 显示音频总时长
            if duration_hours >= 1:
                duration_text = f"音频总时长: {int(duration_hours)}小时{int(duration_minutes % 60)}分钟{int(duration_seconds % 60)}秒"
            else:
                duration_text = f"音频总时长: {int(duration_minutes)}分钟{int(duration_seconds % 60)}秒"
                
            ttk.Label(info_frame, text=duration_text, font=("Microsoft YaHei", 10)).pack(pady=5)
            
            # 显示是否包含字幕
            subtitle_text = "已选择字幕文件" if subtitle_file else "未选择字幕文件"
            ttk.Label(info_frame, text=subtitle_text, font=("Microsoft YaHei", 10)).pack(pady=5)
            
            # 切割方式选择
            method_frame = ttk.LabelFrame(split_dialog, text="切割方式", padding=10)
            method_frame.pack(fill=tk.X, padx=10, pady=10)
            
            split_method = tk.StringVar(value="time")
            
            ttk.Radiobutton(
                method_frame, 
                text="按时间切割", 
                variable=split_method, 
                value="time"
            ).pack(anchor=tk.W, pady=5)
            
            ttk.Radiobutton(
                method_frame, 
                text="按数量切割", 
                variable=split_method, 
                value="count"
            ).pack(anchor=tk.W, pady=5)
            
            # 参数设置框架
            param_frame = ttk.LabelFrame(split_dialog, text="切割参数", padding=10)
            param_frame.pack(fill=tk.X, padx=10, pady=10)
            
            # 时间输入框（分钟）
            time_frame = ttk.Frame(param_frame)
            time_frame.pack(fill=tk.X, pady=5)
            ttk.Label(time_frame, text="每段时长(分钟):").pack(side=tk.LEFT, padx=5)
            time_entry = ttk.Entry(time_frame, width=10)
            time_entry.pack(side=tk.LEFT, padx=5)
            time_entry.insert(0, "120")  # 默认2小时
            
            # 数量输入框
            count_frame = ttk.Frame(param_frame)
            count_frame.pack(fill=tk.X, pady=5)
            ttk.Label(count_frame, text="切割段数:").pack(side=tk.LEFT, padx=5)
            count_entry = ttk.Entry(count_frame, width=10)
            count_entry.pack(side=tk.LEFT, padx=5)
            count_entry.insert(0, str(math.ceil(duration_hours / 2)))  # 默认按2小时计算段数
            
            # 预览信息
            preview_frame = ttk.LabelFrame(split_dialog, text="预览", padding=10)
            preview_frame.pack(fill=tk.X, padx=10, pady=10)
            
            preview_label = ttk.Label(preview_frame, text="", wraplength=350)
            preview_label.pack(pady=5)
            
            # 更新预览信息的函数
            def update_preview(*args):
                try:
                    if split_method.get() == "time":
                        # 按时间切割
                        minutes_per_segment = float(time_entry.get())
                        if minutes_per_segment <= 0:
                            preview_label.config(text="每段时长必须大于0")
                            return
                            
                        seconds_per_segment = minutes_per_segment * 60
                        num_segments = math.ceil(duration_seconds / seconds_per_segment)
                        
                        preview_text = f"将切割成 {num_segments} 段，每段约 {minutes_per_segment} 分钟"
                        
                    else:
                        # 按数量切割
                        num_segments = int(count_entry.get())
                        if num_segments <= 0:
                            preview_label.config(text="段数必须大于0")
                            return
                            
                        seconds_per_segment = duration_seconds / num_segments
                        minutes_per_segment = seconds_per_segment / 60
                        
                        preview_text = f"将切割成 {num_segments} 段，每段约 {minutes_per_segment:.1f} 分钟"
                    
                    preview_label.config(text=preview_text)
                except ValueError:
                    preview_label.config(text="请输入有效的数字")
            
            # 绑定事件
            split_method.trace("w", update_preview)
            time_entry.bind("<KeyRelease>", update_preview)
            count_entry.bind("<KeyRelease>", update_preview)
            
            # 初始更新预览
            update_preview()
            
            # 按钮框架
            button_frame = ttk.Frame(split_dialog)
            button_frame.pack(fill=tk.X, padx=10, pady=10)
            
            # 取消按钮
            ttk.Button(
                button_frame, 
                text="取消", 
                command=split_dialog.destroy
            ).pack(side=tk.RIGHT, padx=5)
            
            # 确定按钮
            def on_confirm():
                try:
                    if split_method.get() == "time":
                        minutes_per_segment = float(time_entry.get())
                        if minutes_per_segment <= 0:
                            messagebox.showerror("错误", "每段时长必须大于0")
                            return
                        seconds_per_segment = minutes_per_segment * 60
                        num_segments = math.ceil(duration_seconds / seconds_per_segment)
                    else:
                        num_segments = int(count_entry.get())
                        if num_segments <= 0:
                            messagebox.showerror("错误", "段数必须大于0")
                            return
                        seconds_per_segment = duration_seconds / num_segments
                    
                    split_dialog.destroy()
                    start_audio_split(audio_file, subtitle_file, num_segments, seconds_per_segment)
                    
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字")
            
            ttk.Button(
                button_frame, 
                text="确定", 
                command=on_confirm
            ).pack(side=tk.RIGHT, padx=5)
            
        except Exception as e:
            messagebox.showerror("错误", f"准备切割时出错：{str(e)}")
            
        # 开始切割处理的函数
        def start_audio_split(audio_file, subtitle_file, num_segments, seconds_per_segment):
            # 创建进度窗口
            progress_window = tk.Toplevel(self.window)
            progress_window.title("处理进度")
            progress_window.geometry("400x150")
            progress_window.transient(self.window)
            
            # 设置进度窗口在主窗口中间
            x = self.window.winfo_x() + (self.window.winfo_width() - 400) // 2
            y = self.window.winfo_y() + (self.window.winfo_height() - 150) // 2
            progress_window.geometry(f"+{x}+{y}")
            
            # 创建进度标签和进度条
            status_label = tk.Label(progress_window, text="正在处理...", wraplength=350)
            status_label.pack(pady=10)
            
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                progress_window, 
                variable=progress_var,
                maximum=100,
                length=350,
                mode='determinate'
            )
            progress_bar.pack(pady=10)
            
            def process_audio():
                try:
                    # 基础目录是音频文件所在的目录
                    base_dir = os.path.dirname(audio_file)
                    base_name = os.path.splitext(os.path.basename(audio_file))[0]
                    ext = os.path.splitext(audio_file)[1]  # 保持原始文件扩展名
                    
                    # 创建新的输出文件夹
                    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_folder_name = f"{base_name}_切割_{current_time}"
                    output_dir = os.path.join(base_dir, output_folder_name)
                    os.makedirs(output_dir, exist_ok=True)
                    print(f"创建输出目录: {output_dir}")
                    
                    # 如果提供了字幕文件，准备字幕相关信息
                    subtitle_base_name = ""
                    if subtitle_file:
                        subtitle_base_name = os.path.splitext(os.path.basename(subtitle_file))[0]
                        # 解析SRT字幕文件
                        with open(subtitle_file, 'r', encoding='utf-8', errors='replace') as f:
                            subtitle_content = f.read()
                        
                        # 解析字幕文件，将其转换为字幕条目列表
                        subtitles = self.parse_srt(subtitle_content)
                    
                    # 总任务数 = 音频段数 + 字幕段数(如果有)
                    total_tasks = num_segments
                    if subtitle_file:
                        total_tasks = num_segments * 2
                    
                    # 当前已完成的任务数
                    completed_tasks = 0
                    
                    # 使用ffmpeg进行音频切割
                    for i in range(num_segments):
                        # 更新进度
                        progress = (completed_tasks / total_tasks) * 100
                        status_label.config(text=f"正在处理音频第 {i+1}/{num_segments} 段...")
                        progress_var.set(progress)
                        progress_window.update()
                        
                        # 计算当前段的时间范围（秒）
                        start_second = i * seconds_per_segment
                        segment_duration = min(seconds_per_segment, duration_seconds - start_second)
                        
                        # 转换为时分秒格式用于显示 - 使用简单的格式，避免特殊字符
                        start_hours = int(start_second // 3600)
                        start_minutes = int((start_second % 3600) // 60)
                        start_seconds = int(start_second % 60)
                        
                        end_second = start_second + segment_duration
                        end_hours = int(end_second // 3600)
                        end_minutes = int((end_second % 3600) // 60)
                        end_seconds = int(end_second % 60)
                        
                        # 使用安全的文件名格式
                        start_time_str = f"{start_hours:02d}h{start_minutes:02d}m{start_seconds:02d}s"
                        end_time_str = f"{end_hours:02d}h{end_minutes:02d}m{end_seconds:02d}s"
                        
                        # 生成输出文件名 - 使用序号和时间范围
                        segment_number = f"{i+1:03d}"
                        output_file = os.path.join(
                            output_dir, 
                            f"{segment_number}_{start_time_str}-{end_time_str}{ext}"
                        )
                        
                        print(f"处理音频段 {i+1}/{num_segments}，输出文件: {output_file}")
                        
                        try:
                            # 规范化路径（确保只使用反斜杠或正斜杠）
                            output_file = os.path.normpath(output_file)
                            
                            # 使用优化后的ffmpeg命令切割音频
                            print(f"开始处理音频段 {i+1}/{num_segments}")
                            print(f"开始时间: {start_second}秒, 持续时间: {segment_duration}秒")
                            
                            cmd = [
                                str(ffmpeg_path),
                                '-y',
                                '-ss', str(start_second),
                                '-i', str(audio_file),
                                '-t', str(segment_duration),
                                '-c', 'copy',
                                '-avoid_negative_ts', '1',
                                '-max_muxing_queue_size', '1024',
                                str(output_file)
                            ]
                            
                            print(f"执行命令: {' '.join(cmd)}")
                            
                            # 在Windows上添加创建无窗口进程的标记
                            creation_flags = 0
                            if platform.system() == 'Windows':
                                creation_flags = subprocess.CREATE_NO_WINDOW
                            
                            # 使用subprocess.run而不是Popen，设置encoding和不使用text参数来避免GBK编码问题
                            process = subprocess.Popen(
                                cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                creationflags=creation_flags
                            )
                            
                            # 等待进程完成，获取二进制输出
                            stdout_data, stderr_data = process.communicate()
                            
                            # 尝试使用UTF-8解码，如果失败则忽略错误
                            try:
                                stderr = stderr_data.decode('utf-8', errors='replace')
                            except:
                                stderr = str(stderr_data)
                            
                            # 检查处理结果
                            if process.returncode != 0:
                                print(f"FFmpeg错误: {stderr}")
                                raise Exception(f"FFmpeg 切割失败：{stderr}")
                            else:
                                print(f"音频段 {i+1} 处理成功")
                            
                            # 验证输出文件
                            if not os.path.exists(output_file):
                                raise Exception(f"输出文件未创建: {output_file}")
                            elif os.path.getsize(output_file) == 0:
                                raise Exception(f"输出文件大小为0字节: {output_file}")
                            
                            completed_tasks += 1
                            
                        except Exception as e:
                            error_msg = f"处理音频段 {i+1} 时出错: {str(e)}"
                            print(error_msg)
                            progress_window.destroy()
                            messagebox.showerror("错误", error_msg)
                            return  # 中断处理
                        
                        # 处理字幕文件（如果有）
                        if subtitle_file:
                            try:
                                progress = (completed_tasks / total_tasks) * 100
                                status_label.config(text=f"正在处理字幕第 {i+1}/{num_segments} 段...")
                                progress_var.set(progress)
                                progress_window.update()
                                
                                print(f"开始处理字幕段 {i+1}/{num_segments}")
                                
                                # 过滤出当前时间段的字幕
                                segment_subtitles = []
                                index = 1
                                for sub in subtitles:
                                    # 更精确的字幕时间匹配逻辑
                                    if (sub['start_time'] >= start_second and sub['start_time'] < start_second + segment_duration) or \
                                       (sub['end_time'] > start_second and sub['end_time'] <= start_second + segment_duration) or \
                                       (sub['start_time'] <= start_second and sub['end_time'] >= start_second + segment_duration):
                                        # 复制字幕并调整时间
                                        adjusted_sub = sub.copy()
                                        adjusted_sub['index'] = index
                                        
                                        # 更精确地调整时间，减去片段开始时间
                                        # 确保字幕开始时间不为负数
                                        adjusted_sub['start_time'] = max(0, sub['start_time'] - start_second)
                                        # 确保字幕结束时间不超出片段时长
                                        adjusted_sub['end_time'] = min(segment_duration, sub['end_time'] - start_second)
                                        
                                        # 只有当调整后的字幕时长大于0才添加
                                        if adjusted_sub['end_time'] > adjusted_sub['start_time']:
                                            segment_subtitles.append(adjusted_sub)
                                            index += 1
                                
                                # 报告处理结果
                                print(f"找到 {len(segment_subtitles)} 条适用于此时间段的字幕")
                                
                                # 生成SRT内容
                                srt_content = self.generate_srt(segment_subtitles)
                                
                                # 保存字幕文件 - 使用与音频相同的命名格式
                                subtitle_output_file = os.path.join(
                                    output_dir, 
                                    f"{segment_number}_{start_time_str}-{end_time_str}.srt"
                                )
                                
                                # 规范化路径
                                subtitle_output_file = os.path.normpath(subtitle_output_file)
                                
                                print(f"保存字幕到文件: {subtitle_output_file}")
                                
                                # 写入文件
                                with open(subtitle_output_file, 'w', encoding='utf-8') as f:
                                    f.write(srt_content)
                                
                                print(f"字幕段 {i+1} 处理成功")
                                completed_tasks += 1
                            
                            except Exception as e:
                                error_msg = f"处理字幕段 {i+1} 时出错: {str(e)}"
                                print(error_msg)
                                # 字幕处理错误不中断整个过程，仅显示警告
                                messagebox.showwarning("警告", error_msg)
                    
                    # 设置进度为100%
                    progress_var.set(100)
                    status_label.config(text="处理完成！")
                    progress_window.update()
                    
                    # 延迟关闭窗口，让用户看到100%的进度
                    progress_window.after(1000, progress_window.destroy)
                    
                    success_message = f"音频切割完成！\\n共切割成 {num_segments} 段\\n"
                    if subtitle_file:
                        success_message += f"字幕也已同步切割\\n"
                    success_message += f"文件保存在：{output_dir}"
                    
                    messagebox.showinfo("完成", success_message)
                    
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("错误", f"处理失败：{str(e)}")
            
            # 创建并启动处理线程
            thread = threading.Thread(target=process_audio)
            thread.daemon = True
            thread.start()

    # 添加一个类变量用于跟踪TTS窗口状态
    tts_window_open = False
    
    def show_tts_window(self):
        """显示配音窗口"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        
        # 检查是否已经打开了配音窗口，防止多开
        if TextSelector.tts_window_open:
            messagebox.showinfo("提示", "配音窗口已经打开，请勿重复开启")
            return
        
        try:
            # 增加窗口计数
            TextSelector.tts_window_open = True
            
            # 诊断信息
            self.logger.info("正在尝试打开配音窗口...")
            
            # 获取TTS目录路径并检查其存在性
            tts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'TTS')
            if not os.path.exists(tts_dir):
                self.logger.error(f"TTS目录不存在: {tts_dir}")
                messagebox.showerror("错误", f"TTS目录不存在，请确保安装完整!\n需要的目录: {tts_dir}")
                TextSelector.tts_window_open = False
                return
                
            # 检查关键文件 (.py 或 .pyd)
            gui_app_py = os.path.join(tts_dir, 'src', 'gui', 'app.py')
            gui_app_pyd = os.path.join(tts_dir, 'src', 'gui', 'app.pyd')
            tts_script = os.path.join(tts_dir, "tts.py")
            
            if not (os.path.exists(gui_app_py) or os.path.exists(gui_app_pyd) or os.path.exists(tts_script)):
                self.logger.error(f"TTS模块关键文件缺失!\n未找到 {gui_app_py}\n或 {gui_app_pyd}\n也未找到 {tts_script}")
                messagebox.showerror("错误", f"TTS模块关键文件缺失!\n未找到:\n{gui_app_py}\n或:\n{gui_app_pyd}\n也未找到:\n{tts_script}\n请重新安装软件。")
                TextSelector.tts_window_open = False
                return
            
            # 列出TTS目录下的文件，帮助诊断
            self.logger.info(f"TTS目录内容: {os.listdir(tts_dir)}")
            
            # 尝试导入方法一：直接导入模块
            try:
                # 确保TTS目录在系统路径中
                if tts_dir not in sys.path:
                    sys.path.append(tts_dir)
                    
                self.logger.info(f"尝试从 {tts_dir} 导入TTS模块")
                
                # 导入TTS应用类
                from TTS.src.gui.app import Application
                
                # 创建主窗口
                root = tk.Toplevel(self.window)
                root.title("文运配音工具")
                
                # 创建TTS应用实例
                app = Application(root)
                
                # 设置窗口图标
                icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "BBB.ico")
                if os.path.exists(icon_path):
                    try:
                        root.iconbitmap(icon_path)
                    except Exception as e:
                        self.logger.warning(f"加载图标失败: {str(e)}")
                
                
                # 添加关闭处理函数
                def on_closing():
                    try:
                        root.destroy()
                        # 重置窗口状态
                        TextSelector.tts_window_open = False
                    except Exception as e:
                        self.logger.error(f"关闭窗口时出错: {str(e)}")
                        # 确保即使出错也重置状态
                        TextSelector.tts_window_open = False
                
                root.protocol("WM_DELETE_WINDOW", on_closing)
                
                return
                
            except ImportError as e:
                self.logger.warning(f"导入TTS模块失败: {str(e)}")
                self.logger.info("尝试使用替代方法启动TTS")
                
                # 尝试方法二：动态导入
                try:
                    if os.path.exists(gui_app_py):
                        self.logger.info(f"尝试使用spec从文件导入: {gui_app_py}")
                        
                        spec = importlib.util.spec_from_file_location("app", gui_app_py)
                        if spec is not None:  # 添加空值检查
                            app_module = importlib.util.module_from_spec(spec)
                            if spec.loader is not None:  # 添加loader空值检查
                                spec.loader.exec_module(app_module)
                            else:
                                raise ImportError("无法加载TTS模块：loader为空")
                        else:
                            raise ImportError("无法创建TTS模块规范")
                        
                        # 创建主窗口
                        root = tk.Toplevel(self.window)
                        root.title("文运配音工具")
                        
                        # 创建TTS应用实例
                        app = app_module.Application(root)
                        
                        # 配置窗口...
                        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "BBB.ico")
                        if os.path.exists(icon_path):
                            try:
                                root.iconbitmap(icon_path)
                            except Exception as e:
                                self.logger.warning(f"加载图标失败: {str(e)}")
                        
                        width = 1280
                        height = 720
                        screen_width = root.winfo_screenwidth()
                        screen_height = root.winfo_screenheight()
                        x = (screen_width - width) // 2
                        y = (screen_height - height) // 2
                        root.geometry(f'{width}x{height}+{x}+{y}')
                        root.minsize(1200, 650)
                        
                        # 添加关闭处理函数
                        def on_closing():
                            try:
                                root.destroy()
                                TextSelector.tts_window_open = False
                            except Exception as e:
                                self.logger.error(f"关闭窗口时出错: {str(e)}")
                                TextSelector.tts_window_open = False
                        
                        root.protocol("WM_DELETE_WINDOW", on_closing)
                        
                        return
                        
                    # 导入失败，尝试方法三：启动独立脚本
                    elif os.path.exists(tts_script):
                        self.logger.info(f"正在启动独立版TTS脚本: {tts_script}")
                        
                        # 创建一个线程来监控进程状态
                        def monitor_standalone_tts():
                            try:
                                # 使用startupinfo隐藏控制台窗口
                                startupinfo = get_subprocess_startupinfo()
                                
                                # 启动Python解释器执行独立脚本
                                python_exe = sys.executable
                                self.logger.info(f"使用Python解释器: {python_exe}")
                                self.logger.info(f"启动脚本: {tts_script}")
                                self.logger.info(f"工作目录: {os.path.dirname(tts_script)}")
                                
                                process = subprocess.Popen(
                                    [python_exe, tts_script],
                                    startupinfo=startupinfo,
                                    cwd=os.path.dirname(tts_script)
                                )
                                
                                # 等待进程结束
                                process.wait()
                                
                                # 进程结束后，更新窗口状态
                                TextSelector.tts_window_open = False
                            except Exception as e:
                                self.logger.error(f"监控独立TTS进程时出错: {str(e)}")
                                TextSelector.tts_window_open = False
                        
                        # 启动监控线程
                        monitor_thread = threading.Thread(target=monitor_standalone_tts)
                        monitor_thread.daemon = True
                        monitor_thread.start()
                        
                        return
                    else:
                        # 导入失败且无独立脚本
                        raise ImportError("找不到TTS应用类或独立脚本")
                
                except Exception as e:
                    self.logger.error(f"替代方法启动TTS失败: {str(e)}")
                    raise  # 重新抛出异常
            
        except Exception as e:
            err_msg = traceback.format_exc()
            self.logger.error(f"启动配音工具失败: {err_msg}")
            messagebox.showerror("错误", f"启动配音工具失败：\n{str(e)}\n\n请检查TTS目录是否完整，或联系开发者。")
            TextSelector.tts_window_open = False

    def format_time(self, seconds):
        """格式化时间为 SRT 格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        milliseconds = int((seconds % 1) * 1000)
        seconds = int(seconds)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
    
    def parse_srt(self, content):
        """解析SRT字幕文件内容"""
        subtitles = []
        # 使用正则表达式以兼容 \n 和 \r\n
        blocks = re.split(r'\r?\n\r?\n', content.strip())
        
        for block in blocks:
            # 使用正则表达式以兼容 \n 和 \r\n
            lines = re.split(r'\r?\n', block.strip())
            if len(lines) >= 3:  # 确保有足够的行
                try:
                    # 获取字幕索引
                    index = int(lines[0])
                    
                    # 解析时间
                    time_line = lines[1]
                    times = time_line.split(' --> ')
                    start_time = self.time_str_to_seconds(times[0])
                    end_time = self.time_str_to_seconds(times[1])
                    
                    # 获取字幕文本
                    text = '\n'.join(lines[2:])
                    
                    # 添加到字幕列表
                    subtitles.append({
                        'index': index,
                        'start_time': start_time,
                        'end_time': end_time,
                        'text': text
                    })
                except Exception as e:
                    print(f"解析字幕块出错: {e}, 块内容: {block}")
                    continue
        
        return subtitles
    
    def time_str_to_seconds(self, time_str):
        """将SRT时间字符串转换为秒数"""
        parts = time_str.replace(',', '.').split(':')
        hours = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        return hours * 3600 + minutes * 60 + seconds
    
    def generate_srt(self, subtitles):
        """从字幕条目列表生成SRT格式内容"""
        srt_content = []
        
        for sub in subtitles:
            # 转换开始时间和结束时间为SRT格式
            start_time = self.seconds_to_time_str(sub['start_time'])
            end_time = self.seconds_to_time_str(sub['end_time'])
            
            # 添加字幕条目
            srt_content.append(str(sub['index']))
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(sub['text'])
            srt_content.append('')  # 添加空行
        
        return '\n'.join(srt_content)
    
    def seconds_to_time_str(self, seconds):
        """将秒数转换为SRT时间格式字符串 (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        whole_seconds = int(seconds)
        milliseconds = int((seconds - whole_seconds) * 1000)
        return f"{hours:02d}:{minutes:02d}:{whole_seconds:02d},{milliseconds:03d}"

    def check_license(self):
        """检查授权状态"""
        try:
            # 尝试加载保存的授权码
            license_code = self.license_manager.load_license()
            if license_code:
                # 验证授权码
                valid, message = self.license_manager.verify_license(license_code)
                if valid:
                    # 解析消息获取授权类型和剩余时间
                    if "永久授权" in message:
                        # 永久授权只显示"永久授权"，不显示剩余时间
                        status_text = "永久授权"
                    else:
                        # 临时授权显示"临时授权"和剩余时间
                        import re
                        # 修正正则表达式顺序，优先匹配更长的模式
                        remaining_match = re.search(r'剩余\s+(\d+\s+天\s+\d+\s+小时|\d+\s+小时\s+\d+\s+分钟|\d+\s+天|\d+\s+小时|\d+\s+分钟)', message)
                        
                        # 使用group(1)只获取时间部分，并修正最终显示格式
                        if remaining_match:
                            remaining_time = remaining_match.group(1)
                            status_text = f"临时授权, 剩余 {remaining_time}"
                        else:
                            status_text = "临时授权 (时长未知)"
                    
                    self.license_status_label_widget.config(text=status_text, foreground="green")
                    self.enable_functions()
                    return True
            
            self.license_status_label_widget.config(text="未授权", foreground="red")
            self.disable_functions()
            return False
            
        except Exception as e:
            messagebox.showerror("错误", f"检查授权失败：{str(e)}")
            self.disable_functions()
            return False
    
    def activate_license(self):
        """激活软件"""
        # 创建激活对话框 - 增大尺寸
        dialog = tk.Toplevel(self.window)
        dialog.title("软件激活")
        dialog.geometry("480x350")  # 增大窗口尺寸
        dialog.resizable(False, False)
        
        # 使对话框居中
        dialog.transient(self.window)
        dialog.grab_set()
        x = self.window.winfo_x() + (self.window.winfo_width() - 480) // 2
        y = self.window.winfo_y() + (self.window.winfo_height() - 350) // 2
        dialog.geometry(f"+{x}+{y}")

        # 创建框架 - 调整内边距
        frame = ttk.Frame(dialog, padding="25")  # 增加内边距
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 获取公告信息
        notice = self.license_manager.get_server_notice()
        if notice and notice != "暂无公告" and notice != "获取公告失败" and not notice.startswith("无法连接"):
            # 显示公告信息
            notice_frame = ttk.LabelFrame(frame, text="公告")
            notice_frame.pack(fill=tk.X, pady=(0, 10))
            notice_label = ttk.Label(notice_frame, text=notice, wraplength=350)
            notice_label.pack(pady=5, padx=5)
        
        # 机器码显示
        ttk.Label(frame, text="机器码:", font=('Microsoft YaHei', 10)).pack(anchor=tk.W)
        machine_code_entry = ttk.Entry(frame, width=50)  # 增加宽度
        machine_code_entry.insert(0, self.machine_code)
        machine_code_entry.configure(state='readonly')
        machine_code_entry.pack(pady=(0, 5), fill=tk.X)  # 添加fill选项使其填充整个宽度
        
        # 复制机器码按钮
        ttk.Button(frame, text="复制机器码", 
                  command=lambda: pyperclip.copy(self.machine_code)).pack(pady=(0, 15))
        
        # 授权码输入
        ttk.Label(frame, text="请输入授权码:", font=('Microsoft YaHei', 10)).pack(anchor=tk.W)
        license_entry = ttk.Entry(frame, width=50)  # 增加宽度
        license_entry.pack(pady=(0, 20), fill=tk.X)  # 添加fill选项使其填充整个宽度
        
        def validate_license():
            code = license_entry.get().strip()
            if not code:
                messagebox.showwarning("警告", "请输入授权码！")
                return
            
            try:
                # 验证授权码
                valid, message = self.license_manager.verify_license(code)
                if valid:
                    # 保存授权码
                    if self.license_manager.save_license(code):
                        messagebox.showinfo("成功", "软件已成功激活！")
                        self.check_license()
                        dialog.destroy()
                    else:
                        messagebox.showerror("错误", "授权码保存失败")
                else:
                    messagebox.showerror("错误", message)
            except Exception as e:
                messagebox.showerror("错误", f"激活失败：{str(e)}")
        
        def unbind_license():
            code = license_entry.get().strip()
            if not code:
                messagebox.showwarning("警告", "请输入要解绑的授权码！")
                return
            
            try:
                # 确认解绑
                if messagebox.askyesno("确认解绑", "确定要解绑当前设备的授权吗?\n解绑后可在其他设备上使用此授权码。"):
                    # 执行解绑
                    success, message = self.license_manager.unbind_license(code)
                    if success:
                        messagebox.showinfo("成功", "授权已成功解绑！")
                        self.check_license()
                        dialog.destroy()
                    else:
                        messagebox.showerror("错误", message)
            except Exception as e:
                messagebox.showerror("错误", f"解绑失败：{str(e)}")
        
        # 按钮区域 - 优化布局，增加按钮间距
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=15)  # 增加上下边距
        
        # 创建一个中间框架来容纳按钮
        center_frame = ttk.Frame(btn_frame)
        center_frame.pack(anchor=tk.CENTER)  # 居中放置
        
        # 确认按钮
        confirm_btn = ttk.Button(center_frame, 
                               text="确认激活",
                               width=16,  # 增加宽度
                               command=validate_license)
        confirm_btn.pack(side=tk.LEFT, padx=8)  # 增加按钮间距
        
        # 解绑按钮
        unbind_btn = ttk.Button(center_frame, 
                              text="解绑授权",
                              width=16,  # 增加宽度
                              command=unbind_license)
        unbind_btn.pack(side=tk.LEFT, padx=8)  # 增加按钮间距
        
        # 取消按钮
        cancel_btn = ttk.Button(center_frame, 
                              text="取消",
                              width=16,  # 增加宽度
                              command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=8)  # 增加按钮间距
        
        # 绑定回车键
        license_entry.bind('<Return>', lambda e: validate_license())
        
        # 设置焦点
        license_entry.focus()

    def disable_functions(self):
        """禁用所有功能"""
        # 禁用所有按钮和输入框
        for widget in self.window.winfo_children():
            self._disable_widget_recursive(widget)

    def _disable_widget_recursive(self, widget):
        """递归禁用所有控件"""
        try:
            # 检查是否是激活按钮
            if widget.winfo_name() == 'activate_button':
                return  # 跳过激活按钮
            
            # 禁用其他控件
            if isinstance(widget, (ttk.Button, ttk.Entry, tk.Button, tk.Entry)):
                widget.configure(state='disabled')
            # 处理子控件
            for child in widget.winfo_children():
                self._disable_widget_recursive(child)
        except:
            pass  # 忽略不能配置的控件

    def enable_functions(self):
        """启用所有功能"""
        # 启用所有按钮和输入框
        for widget in self.window.winfo_children():
            self._enable_widget_recursive(widget)

    def _enable_widget_recursive(self, widget):
        """递归启用所有控件"""
        try:
            if isinstance(widget, (ttk.Button, ttk.Entry, tk.Button, tk.Entry)):
                widget.configure(state='normal')
            # 处理子控件
            for child in widget.winfo_children():
                self._enable_widget_recursive(child)
        except:
            pass  # 忽略不能配置的控件

    def show_video_editor(self):
        """打开视频编辑器"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
            
        # 检查已打开的视频编辑器窗口数量
        if self.video_editor_count >= 2:
            messagebox.showwarning("警告", "最多只能打开2个视频编辑器窗口！")
            return
            
        try:
            # 尝试多个可能的路径
            possible_paths = [
                # 固定路径
                r"F:\\哔哩哔哩\\CR综合处理-1652\\CRVideoMate.exe",
                
                # 相对路径
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'CR综合处理-1652', 'CRVideoMate.exe'),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'CRVideoMate.exe'),
                
                # 其他常见路径
                r"D:\\哔哩哔哩\\CR综合处理-1652\\CRVideoMate.exe",
                r"E:\\哔哩哔哩\\CR综合处理-1652\\CRVideoMate.exe",
                r"C:\\哔哩哔哩\\CR综合处理-1652\\CRVideoMate.exe",
            ]
            
            # 查找可用的编辑器路径
            editor_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    editor_path = path
                    break
            
            if not editor_path:
                error_message = (
                    "找不到视频编辑器程序！\n\n"
                    "请确保以下任一位置存在CRVideoMate.exe：\n"
                    f"1. {possible_paths[0]} (推荐路径)\n"
                    f"2. {possible_paths[1]}\n"
                    "或将CRVideoMate.exe放在程序所在目录"
                )
                messagebox.showerror("错误", error_message)
                return
            
            # 修改进程启动方式
            process = subprocess.Popen(editor_path, 
                                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
            self.subprocesses.append(process)
            
            # 增加计数器
            self.video_editor_count += 1
            
            # 创建监控线程检查进程是否结束
            def monitor_process():
                process.wait()
                self.video_editor_count -= 1
            
            monitor_thread = threading.Thread(target=monitor_process, daemon=True)
            self.threads.append(monitor_thread)
            monitor_thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"无法打开视频编辑器：{str(e)}\n\n程序路径：{editor_path if 'editor_path' in locals() else '未找到'}")

    def show_auto_shutdown(self):
        """打开自动关机程序"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        
        # 创建答题对话框
        answer_dialog = tk.Toplevel(self.window)
        answer_dialog.title("安全验证")
        answer_dialog.geometry("600x400")
        answer_dialog.transient(self.window)
        answer_dialog.grab_set()
        
        # 居中显示
        x = self.window.winfo_x() + (self.window.winfo_width() - 400) // 2
        y = self.window.winfo_y() + (self.window.winfo_height() - 300) // 2
        answer_dialog.geometry(f"+{x}+{y}")
        
        # 问题文本
        question_text = """请计算出下面有关《我的世界》计算题的答案：
        
第一个数字:游戏中每个玩家的初始生命值
      
第二个数字:玩家背包中泥土最多堆叠的数量
    
第三个数字:末影珍珠最大堆叠的数量
    
第四个数字:众所周知木搞的耐久度只有多少

请计算出1+2+3+4的答案："""
        
        ttk.Label(answer_dialog, text=question_text, wraplength=380).pack(pady=10, padx=20)
        
        # 答案输入框
        answer_var = tk.StringVar()
        answer_entry = ttk.Entry(answer_dialog, textvariable=answer_var)
        answer_entry.pack(pady=10)
        
        def verify_answer():
            if answer_var.get().upper() == "103":
                try:
                    from out import AutoShutdownGUI
                    # 先创建自动关机窗口
                    auto_shutdown = AutoShutdownGUI()
                    # 销毁答题对话框
                    answer_dialog.destroy()
                    # 确保自动关机窗口显示并在最前
                    auto_shutdown.window.deiconify()
                    auto_shutdown.window.focus_force()
                    auto_shutdown.window.lift()
                    # 运行自动关机窗口的主循环
                    auto_shutdown.window.mainloop()
                except ImportError as e:
                    messagebox.showerror("错误", f"无法导入自动关机模块：{str(e)}\n请确认无.py文件存在。")
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开自动关机程序：{str(e)}")
            else:
                messagebox.showerror("错误", "答案错误，请重试！")
                answer_var.set("")  # 清空输入框
        
        # 确认按钮
        ttk.Button(answer_dialog, text="确认", command=verify_answer).pack(pady=10)
        
        # 绑定回车键
        answer_entry.bind('<Return>', lambda e: verify_answer())
        
        # 设置焦点
        answer_entry.focus()

    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出程序吗?\n将关闭所有附属程序"
                                  ):
            self.cleanup()
            self.window.destroy()
            # 强制退出程序
            os._exit(0)

    def create_thread(self, target, daemon=True):
        """创建并记录线程"""
        thread = threading.Thread(target=target, daemon=True)
        self.threads.append(thread)
        return thread
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        
        # 重置视频编辑器计数器和小蜗工具箱计数器
        self.video_editor_count = 0
        self.xiaowotool_count = 0
        
        # 终止所有子进程
        for proc in self.subprocesses:
            try:
                # 先尝试正常终止
                proc.terminate()
                # 等待进程终止，设置较短的超时时间
                proc.wait(timeout=0.5)
            except:
                try:
                    # 如果正常终止失败，强制结束
                    proc.kill()
                except:
                    pass
        
        # 确保在Windows上彻底关闭所有视频编辑器进程
        if platform.system() == 'Windows':
            try:
                # 使用taskkill强制结束所有CRVideoMate.exe进程及其子进程
                subprocess.run(['taskkill', '/F', '/T', '/IM', 'CRVideoMate.exe'], 
                             stdout=subprocess.DEVNULL,
                             stderr=subprocess.DEVNULL,
                             timeout=1)  # 添加超时限制
                
                # 重置小蜗工具箱计数器
                self.xiaowotool_count = 0
                
            except:
                pass
        
        # 终止所有线程
        for thread in self.threads:
            if thread and thread.is_alive():
                try:
                    thread.join(timeout=0.1)
                except:
                    pass
        
        # 清空列表
        self.threads.clear()
        self.subprocesses.clear()
        
        # 刷新输出缓冲区
        try:
            sys.stdout.flush()
            sys.stderr.flush()
        except:
            pass

    def open_text_editor(self):
        """打开文本编辑器"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        
        if not hasattr(self, 'file_path_str') or not self.file_path_str:
            messagebox.showwarning("警告", "请先选择文本文件！")
            return
        
        TextEditor(self.window, self.file_path_str, self)

    def open_current_file(self):
        """打开当前文本文件"""
        if not hasattr(self, 'file_path_str') or not self.file_path_str:
            messagebox.showwarning("警告", "请先选择文本文件！")
            return
        
        try:
            os.startfile(self.file_path_str)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件：{str(e)}")

    def open_lightweight_browser(self):
        """打开轻量浏览器"""
        try:
            # 使用系统默认浏览器打开网页
            import webbrowser
            webbrowser.open('http://mi.zzlye.shop:80')
            print("已在系统默认浏览器中打开链接")
        except Exception as e:
            print(f"打开浏览器时出错: {str(e)}")
            messagebox.showerror("错误", f"打开浏览器失败: {str(e)}")
            
    def open_doc_link(self, url):
        """打开文档链接"""
        try:
            # 使用系统默认浏览器打开
            import webbrowser
            webbrowser.open(url)
            print(f"已在系统默认浏览器中打开链接: {url}")
        except Exception as e:
            print(f"打开链接时出错: {str(e)}")
            messagebox.showerror("错误", f"无法打开链接: {str(e)}")

    def _detect_gpu_encoders(self):
        """检测可用的GPU编码器"""
        available_encoders = []
        
        # 获取ffmpeg路径
        ffmpeg_path = get_ffmpeg_path()
        if not ffmpeg_path:
            return available_encoders
        
        try:
            # 运行ffmpeg查询所有可用编码器
            cmd = [ffmpeg_path, "-encoders"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            encoders_output = result.stdout
            
            # 检查是否有GPU编码器
            if "h264_nvenc" in encoders_output:  # NVIDIA GPU
                available_encoders.append(("h264_nvenc", "NVIDIA GPU (H.264)"))
            if "hevc_nvenc" in encoders_output:  # NVIDIA GPU HEVC
                available_encoders.append(("hevc_nvenc", "NVIDIA GPU (HEVC/H.265)"))
            if "h264_amf" in encoders_output:  # AMD GPU
                available_encoders.append(("h264_amf", "AMD GPU (H.264)"))
            if "hevc_amf" in encoders_output:  # AMD GPU HEVC
                available_encoders.append(("hevc_amf", "AMD GPU (HEVC/H.265)"))
            if "h264_qsv" in encoders_output:  # Intel GPU
                available_encoders.append(("h264_qsv", "Intel GPU (H.264)"))
            if "hevc_qsv" in encoders_output:  # Intel GPU HEVC
                available_encoders.append(("hevc_qsv", "Intel GPU (HEVC/H.265)"))
            
            # 如果没有找到GPU编码器，尝试检测系统GPU
            if not available_encoders:
                # NVIDIA GPU检测
                try:
                    # 尝试运行nvidia-smi命令检测NVIDIA GPU
                    nvidia_result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
                    if nvidia_result.returncode == 0:
                        # 找到NVIDIA GPU，但编码器可能未在FFmpeg中启用
                        available_encoders.append(("h264_nvenc", "NVIDIA GPU (H.264) - 可能需要更新FFmpeg"))
                except:
                    pass
                
                # AMD GPU检测 (Windows)
                if platform.system() == "Windows":
                    try:
                        # 检查AMD显卡驱动是否存在
                        amd_path = r"C:\\Windows\\System32\\DriverStore\\FileRepository"
                        if os.path.exists(amd_path):
                            for dir_name in os.listdir(amd_path):
                                if "amd" in dir_name.lower() and "display" in dir_name.lower():
                                    available_encoders.append(("h264_amf", "AMD GPU (H.264) - 可能需要更新FFmpeg"))
                                    break
                    except:
                        pass
            
            return available_encoders
        except Exception as e:
            print(f"检测GPU编码器时出错: {str(e)}")
            return []

    def _build_function_buttons(self, main_frame):
        """构建功能按钮区域"""
        functions_frame = ttk.LabelFrame(main_frame, text="功能区")
        functions_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建功能按钮网格
        button_frame = ttk.Frame(functions_frame)
        button_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 定义按钮信息
        buttons = [
            ("选择文本文件", self.select_file),
            ("格式化文本", self.format_text),
            ("自定义分段", self.custom_cut),
            ("恢复上次分段", self.restore_last_cut),
            ("合并音频和字幕", self.merge_audio),
            ("分割音频和字幕", self.split_audio),
            ("小蜗工具箱", self.show_xiaowo_tool),
            ("自动配音", self.show_tts_window),
            ("自动关机", self.show_auto_shutdown),
            ("视频编辑", self.show_video_editor),
            ("自动合成视频", self.show_video_processor),
            ("文本编辑器", self.open_text_editor),
            ("备份文本", self.backup_file),
            ("恢复备份", self.restore_backup),
            ("素材下载", self.open_lightweight_browser),
        ]
        
        # 计算按钮排列
        num_buttons = len(buttons)
        cols = min(4, num_buttons)  # 最多4列
        rows = (num_buttons + cols - 1) // cols  # 计算需要的行数
        
        # 设置统一的按钮样式
        button_style = 'Main.TButton'
        
        # 创建按钮
        for i, (text, command) in enumerate(buttons):
            row = i // cols
            col = i % cols
            
            # 创建按钮容器
            btn_frame = ttk.Frame(button_frame)
            btn_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
            
            # 添加按钮 - 设置统一宽度和高度
            btn = ttk.Button(btn_frame, 
                      text=text, 
                      command=command,
                      style=button_style)
            btn.pack(fill=tk.BOTH, expand=True, ipady=2)  # 添加内边距使按钮更高
            
            # 添加特殊标签
            if text == "视频编辑":
                ttk.Label(btn_frame,
                         text="禁止更新！",
                         style='Small.TLabel',
                         foreground='#FF0000').pack(pady=(0, 3))
            # 删除自动合成视频标签
            elif text == "自动关机":
                ttk.Label(btn_frame, 
                         text="此按钮为一次性按钮，关闭自动关机窗口软件也会被关闭",
                         style='Small.TLabel',
                         font=('Microsoft YaHei', 8, 'bold'),
                         foreground='#FF0000').pack(pady=(0, 3))
            elif text == "小蜗工具箱":
                ttk.Label(btn_frame,
                         text="文本分割和音频音量调整工具",
                         style='Small.TLabel',
                         foreground='#0066CC').pack(pady=(0, 3))
        
        # 设置列和行的权重，使按钮均匀分布
        for i in range(cols):
            button_frame.columnconfigure(i, weight=1)
        for i in range(rows):
            button_frame.rowconfigure(i, weight=1)

    # 在类中添加新方法，放在show_auto_shutdown方法下面
    def show_video_processor(self):
        """打开自动合成视频程序"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return

        # 检查已打开的窗口数量
        if self.video_processor_count >= 1:  # 限制只能打开一个
            messagebox.showwarning("警告", "只能打开一个自动合成视频窗口！")
            return

        try:
            # 增加窗口计数
            self.video_processor_count += 1
         
            # 假设 auto.py 中包含一个名为 AutoVideoProcessor 的主类
            from auto import VideoProcessingApp

            # 创建新窗口
            root = tk.Toplevel(self.window)
            root.title("自动合成视频")

            # 创建应用实例, 并传递scale_factor
            app = VideoProcessingApp(root)

            # 设置窗口图标
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "BBB.ico")
            if os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                except Exception as e:
                    self.logger.warning(f"加载图标失败: {str(e)}")

            # 添加关闭处理函数
            def on_closing():
                try:
                    # 可以在这里添加应用关闭前的清理逻辑
                    root.destroy()
                    # 减少窗口计数
                    self.video_processor_count -= 1
                except Exception as e:
                    self.logger.error(f"关闭窗口时出错: {str(e)}")
                    # 确保即使出错也减少计数
                    self.video_processor_count -= 1
            
            root.protocol("WM_DELETE_WINDOW", on_closing)

        except Exception as e:
            # 出错时减少窗口计数
            self.video_processor_count -= 1
            self.logger.error(f"启动视频处理程序时出错: {str(e)}")
            messagebox.showerror("错误", f"启动视频处理程序时出错: {str(e)}")

    def show_xiaowo_tool(self):
        """打开小蜗工具箱"""
        if not self.check_license():
            messagebox.showerror("错误", "软件未授权，请先激活！")
            return
        
        # 检查是否已经打开了小蜗工具箱窗口
        if self.xiaowotool_count > 0:
            messagebox.showwarning("警告", "只能打开一个小蜗工具箱窗口！")
            return
            
        try:
            # 增加窗口计数
            self.xiaowotool_count += 1
            
            # 导入小工具模块
            try:
                # 先尝试从小工具模块导入
                from tool import TextSplitterAndAudioAdjuster
                
                # 创建主窗口
                root = tk.Toplevel(self.window)
                root.title("小蜗工具箱")
                
                # 创建工具箱应用实例
                app = TextSplitterAndAudioAdjuster(root)
                
                # 设置窗口图标
                icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "BBB.ico")
                if os.path.exists(icon_path):
                    try:
                        root.iconbitmap(icon_path)
                    except Exception as e:
                        self.logger.warning(f"加载图标失败: {str(e)}")
                
                # 添加关闭处理函数
                def on_closing():
                    try:
                        root.destroy()
                        # 减少窗口计数
                        self.xiaowotool_count -= 1
                    except Exception as e:
                        self.logger.error(f"关闭窗口时出错: {str(e)}")
                        # 确保即使出错也减少计数
                        self.xiaowotool_count -= 1
                
                root.protocol("WM_DELETE_WINDOW", on_closing)
                
            except ImportError:
                # 导入失败，提示错误
                self.xiaowotool_count -= 1
                messagebox.showerror("错误", "无法导入小工具模块，请确保程序完整。")
                return
            
        except Exception as e:
            # 出错时减少窗口计数
            self.xiaowotool_count -= 1
            self.logger.error(f"启动小蜗工具箱时出错: {str(e)}")
            messagebox.showerror("错误", f"启动小蜗工具箱时出错: {str(e)}")

    def _create_card(self, parent, title, description=""):
        """创建带标题和描述的卡片"""
        card_frame = ttk.Frame(parent, style='Card.TFrame', padding=(10, 10, 10, 10))
        
        # 添加圆角边框效果（通过在ttk样式中添加）
        card_frame.bind("<Map>", lambda e: self._apply_card_border_radius(card_frame))
        
        if title:
            title_label = ttk.Label(card_frame, text=title, style='CardTitle.TLabel')
            title_label.pack(anchor='w', pady=(0, 5))
        
        if description:
            desc_label = ttk.Label(card_frame, text=description, style='Description.TLabel', wraplength=self.min_width_for_cards)
            desc_label.pack(anchor='w', pady=(0, 10))
        
        return card_frame
    
    def _apply_card_border_radius(self, card):
        """为卡片应用圆角边框效果"""
        # 尝试应用圆角边框效果（如果平台支持）
        try:
            # 仅在Windows上使用此方法
            if sys.platform == "win32":
                card.update_idletasks()  # 确保组件已经绘制
                card_id = str(card)
                self.window.eval(f"""
                    {{
                        # 尝试应用圆角，仅适用于支持的平台
                        package require Tk 8.6
                        catch {{
                            ttk::style configure Card.TFrame -borderradius 8
                        }}
                    }}
                """)
        except Exception:
            # 如果圆角应用失败，忽略错误
            pass
            
    def _create_button(self, parent, text, command, width=None, style="TButton", image=None, compound=tk.LEFT, sticky=None, **kwargs):
        """创建统一风格的按钮"""
        # 修复linter错误，确保image和compound参数类型正确
        if isinstance(compound, str):
            # 将字符串类型的compound转换为tk常量
            compound_map = {
                "left": tk.LEFT,
                "right": tk.RIGHT,
                "top": tk.TOP,
                "bottom": tk.BOTTOM,
                "center": tk.CENTER,
                "none": None,
                "": None
            }
            compound = compound_map.get(compound.lower(), tk.LEFT)
        
        # 如果image为None，则不传递image参数
        if image is None:
            btn = ttk.Button(parent, text=text, command=command, style=style, compound=compound, **kwargs)
        else:
            btn = ttk.Button(parent, text=text, command=command, style=style, image=image, compound=compound, **kwargs)
        
        # 设置比例边框填充
        if width:
            btn.configure(width=width)
        
        # 添加悬停效果
        btn.bind("<Enter>", lambda e: btn.configure(cursor="hand2"))
        btn.bind("<Leave>", lambda e: btn.configure(cursor=""))
        
        # 应用粘连方式
        if sticky:
            btn.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        else:
            btn.pack(fill=tk.X, padx=5, pady=5)
            
        return btn

    # 删除了所有与主题相关的方法


class TextEditor:
    def __init__(self, parent, file_path=None, text_selector=None):
        # 创建新窗口
        self.window = tk.Toplevel(parent)
        self.window.title(f"文本编辑器 - {os.path.basename(file_path) if file_path else '未命名'} - 售价499元")
        self.text_selector = text_selector  # 保存TextSelector实例的引用
        
        # 获取DPI缩放比例
        try:
            self.scale_factor = ctypes.windll.shcore.GetScaleFactorForDevice(0) / 100
        except:
            screen_width = self.window.winfo_screenwidth()
            self.scale_factor = screen_width / 1920
        
        # 根据DPI缩放调整基础尺寸
        window_width = int(800 * self.scale_factor)
        window_height = int(600 * self.scale_factor)
        
        # 设置窗口大小和位置
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 创建主框架来容纳左侧面板和编辑区
        self.main_frame = ttk.PanedWindow(self.window, orient=tk.HORIZONTAL)
        self.main_frame.pack(expand=True, fill='both')
        
        # 创建左侧搜索结果面板
        self.search_panel = ttk.Frame(self.main_frame)  # 移除固定宽度设置
        self.main_frame.add(self.search_panel, weight=1)  # 左侧面板权重设置为1
        
        # 设置最小宽度
        self.search_panel.grid_propagate(False)  # 禁止自动调整大小
        self.search_panel.config(width=50)  # 设置最小宽度为100
        
        # 搜索结果标题
        search_header = ttk.Frame(self.search_panel)
        search_header.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(search_header, text="搜索结果", font=('Microsoft YaHei', int(10 * self.scale_factor), 'bold')).pack(side=tk.LEFT)
        
        # 创建搜索结果列表
        self.result_frame = ttk.Frame(self.search_panel)
        self.result_frame.pack(fill=tk.BOTH, expand=True, padx=5)
        
        # 创建带滚动条的结果列表
        result_scroll = ttk.Scrollbar(self.result_frame)
        result_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 将 Listbox 替换为 Text 组件
        self.result_list = tk.Text(self.result_frame, 
                                font=('Microsoft YaHei', int(9 * self.scale_factor)),
                                wrap=tk.WORD,
                                height=10)
        self.result_list.pack(fill=tk.BOTH, expand=True)
        
        # 配置结果列表的标签样式
        self.result_list.tag_configure("match", foreground="red", font=("Microsoft YaHei", int(9 * self.scale_factor), "bold"))
        self.result_list.tag_configure("current", background="lightgray")
        
        # 绑定点击事件
        self.result_list.bind('<ButtonRelease-1>', self.on_result_click)
        
        # 配置滚动条
        result_scroll.config(command=self.result_list.yview)
        self.result_list.config(yscrollcommand=result_scroll.set)
        
        # 禁用输入
        self.result_list.config(state=tk.DISABLED)  # 设置为只读
        
        # 创建右侧编辑区框架
        self.edit_frame = ttk.Frame(self.main_frame)
        self.main_frame.add(self.edit_frame, weight=4)  # 右侧面板权重设置为4
        
        # 创建文本区域
        self.text_area = tk.Text(self.edit_frame, wrap=tk.WORD, undo=True, font=('Microsoft YaHei', int(12 * self.scale_factor)))
        self.text_area.grid(row=0, column=0, sticky="nsew")  # 使用 grid 布局
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(self.edit_frame, command=self.text_area.yview)
        scrollbar.grid(row=0, column=1, sticky='ns')  # 使用 grid 布局
        self.text_area.config(yscrollcommand=scrollbar.set)
        
        # 设置行列权重以确保文本区域和滚动条能够扩展
        self.edit_frame.grid_rowconfigure(0, weight=1)
        self.edit_frame.grid_columnconfigure(0, weight=1)
        
        # 底部状态栏
        bottom_frame = ttk.Frame(self.window)
        bottom_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态栏
        self.status_bar = ttk.Label(bottom_frame, text="就绪", font=('Microsoft YaHei', int(10 * self.scale_factor)))
        self.status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 初始化变量
        self.current_file = file_path
        self.search_window = None
        self.matches = []  # 存储搜索匹配结果
        self.current_match = 0  # 当前匹配项索引
        self.search_after_id = None  # 用于搜索延迟
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 绑定事件
        self.text_area.bind('<KeyRelease>', self.update_status)
        self.text_area.bind('<Button-1>', self.update_status)
        self.text_area.bind('<ButtonRelease-1>', self.update_status)
        self.text_area.bind('<<Modified>>', self._on_text_modified)
        self.text_modified_pending = False
        
        # 配置文本区域的高亮样式
        self.text_area.tag_configure("search", background="yellow")
        self.text_area.tag_configure("current_match", background="lightgreen")
        
        # 加载文件内容
        if file_path:
            self.load_file(file_path)
    
    def create_menu(self):
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="保存", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_command(label="另存为", command=self.save_as_file, accelerator="Ctrl+Shift+S")  # 新增
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.window.destroy)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="撤销", command=self.text_area.edit_undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="重做", command=self.text_area.edit_redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="剪切", command=lambda: self.text_area.event_generate("<<Cut>>"), accelerator="Ctrl+X")
        edit_menu.add_command(label="复制", command=lambda: self.text_area.event_generate("<<Copy>>"), accelerator="Ctrl+C")
        edit_menu.add_command(label="粘贴", command=lambda: self.text_area.event_generate("<<Paste>>"), accelerator="Ctrl+V")
        edit_menu.add_separator()
        edit_menu.add_command(label="查找", command=self.show_search_window, accelerator="Ctrl+F")
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        
        # 字体大小子菜单
        font_size_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="字体大小", menu=font_size_menu)
        
        # 添加字体大小选项
        font_sizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36]
        self.current_font_size = tk.IntVar(value=12)  # 默认字体大小
        
        def change_font_size(size):
            self.current_font_size.set(size)
            current_font = ('Microsoft YaHei', size)  # 使用微软雅黑字体
            self.text_area.configure(font=current_font)
        
        for size in font_sizes:
            font_size_menu.add_radiobutton(
                label=f"{size}",
                variable=self.current_font_size,
                value=size,
                command=lambda s=size: change_font_size(s)
            )
        
        # 设置初始字体
        self.text_area.configure(font=('Microsoft YaHei', self.current_font_size.get()))
        
        # 绑定快捷键
        self.window.bind('<Control-s>', lambda e: self.save_file())
        self.window.bind('<Control-f>', lambda e: self.show_search_window())
        
        # 绑定Ctrl+滚轮调整字体大小
        def on_mousewheel(event):
            if event.state & 0x4:  # Ctrl键被按下
                current_size = self.current_font_size.get()
                if event.delta > 0 and current_size < 36:  # 向上滚动，放大字体
                    new_size = min(36, current_size + 1)
                    change_font_size(new_size)
                elif event.delta < 0 and current_size > 8:  # 向下滚动，缩小字体
                    new_size = max(8, current_size - 1)
                    change_font_size(new_size)
                return "break"  # 阻止默认的滚动行为
        
        # 绑定鼠标滚轮事件
        self.text_area.bind('<MouseWheel>', on_mousewheel)
    
    def create_toolbar(self):
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=5, pady=2)
        
        # 添加工具栏按钮
        ttk.Button(toolbar, text="保存", command=self.save_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="另存为", command=self.save_as_file).pack(side=tk.LEFT, padx=2)  # 新增
        ttk.Button(toolbar, text="刷新", command=self.refresh_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="查找", command=self.show_search_window).pack(side=tk.LEFT, padx=2)
    
    def load_file(self, file_path):
        try:
            # 只读取文件的前4KB来检测编码，避免大文件导致的卡顿
            with open(file_path, 'rb') as file:
                raw_data = file.read(4096)
                detected = detect(raw_data)
                encoding = detected['encoding']
            
            # 优先使用检测到的编码
            try:
                with open(file_path, 'r', encoding=encoding or 'utf-8') as file:
                    content = file.read()
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(1.0, content)
                self.update_status()
                return
            except UnicodeDecodeError:
                pass
            
            # 如果检测到的编码失败，尝试最常用的几种编码
            common_encodings = ['utf-8', 'gbk', 'gb18030']
            for encoding in common_encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                    self.text_area.delete(1.0, tk.END)
                    self.text_area.insert(1.0, content)
                    self.update_status()
                    return
                except UnicodeDecodeError:
                    continue
            
            # 如果常用编码都失败，提示用户手动选择
            chosen_encoding = simpledialog.askstring("选择编码",
                                                   "无法自动识别编码，请手动输入编码格式：\n"
                                                   "常用编码：gbk, gb18030, big5, shift_jis等",
                                                   parent=self.window)
            if chosen_encoding:
                try:
                    with open(file_path, 'r', encoding=chosen_encoding) as file:
                        content = file.read()
                    self.text_area.delete(1.0, tk.END)
                    self.text_area.insert(1.0, content)
                    self.update_status()
                except UnicodeDecodeError:
                    messagebox.showerror("错误", f"使用 {chosen_encoding} 编码无法读取文件")
            else:
                messagebox.showwarning("警告", "已取消文件加载")
                
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件：{str(e)}")
    
    def save_file(self):
        """修改原有的保存功能，添加另存为的选项"""
        try:
            # 检查是否有当前文件
            if not self.current_file:
                # 如果没有当前文件，调用另存为
                return self.save_as_file()
                
            content = self.text_area.get(1.0, tk.END)
            
            # 检测文件编码
            file_encoding = 'utf-8'  # 默认编码
            try:
                with open(self.current_file, 'rb') as f:
                    rawdata = f.read(4096)
                    result = detect(rawdata)
                    if result['encoding']:
                        file_encoding = result['encoding']
            except:
                pass
            
            # 保存文件
            with open(self.current_file, 'w', encoding=file_encoding, errors='ignore') as file:
                file.write(content)
            
            self.status_bar.config(text=f"文件已保存 (编码: {file_encoding})")
            return True
            
        except PermissionError:
            # 如果保存失败，提示用户另存为
            response = messagebox.askquestion("错误", 
                "保存失败：权限被拒绝。\n是否尝试另存为新文件？")
            if response == 'yes':
                return self.save_as_file()
            return False
        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")
            return False

    def save_as_file(self):
        """另存为功能"""
        try:
            # 获取当前文件的目录作为初始目录
            initial_dir = os.path.dirname(self.current_file) if self.current_file else os.getcwd()
            
            # 打开文件选择对话框
            new_file = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialdir=initial_dir,
                title="另存为"
            )
            
            if new_file:
                content = self.text_area.get(1.0, tk.END)
                
                # 检测原文件编码（如果存在）
                file_encoding = 'utf-8'  # 默认编码
                if self.current_file and os.path.exists(self.current_file):
                    try:
                        with open(self.current_file, 'rb') as f:
                            rawdata = f.read(4096)
                            result = detect(rawdata)
                            if result['encoding']:
                                file_encoding = result['encoding']
                    except:
                        pass
                
                # 保存文件
                with open(new_file, 'w', encoding=file_encoding, errors='ignore') as file:
                    file.write(content)
                
                # 更新当前文件路径和窗口标题
                self.current_file = new_file
                self.window.title(f"文本编辑器 - {os.path.basename(new_file)}")
                
                # 更新状态栏
                self.status_bar.config(text=f"文件已保存为：{new_file} (编码: {file_encoding})")
                return True
                
        except PermissionError:
            messagebox.showerror("错误", "保存失败：权限被拒绝，请检查文件是否被占用或路径是否正确。")
            return False
        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")
            return False

    def show_search_window(self):
        """显示搜索窗口"""
        if self.search_window:
            self.search_window.focus_force()
            return
        
        self.search_window = tk.Toplevel(self.window)
        self.search_window.title("查找和替换")
        self.search_window.geometry("300x250")
        self.search_window.transient(self.window)
        
        # 设置窗口位置
        x = self.window.winfo_x() + (self.window.winfo_width() - 300) // 2
        y = self.window.winfo_y() + (self.window.winfo_height() - 250) // 2
        self.search_window.geometry(f"+{x}+{y}")
        self.search_window.resizable(False, False)
        
        # 主框架
        main_frame = ttk.Frame(self.search_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 查找框
        ttk.Label(main_frame, text="查找内容:").pack(pady=(5,2))
        self.search_entry = ttk.Entry(main_frame)
        self.search_entry.pack(fill=tk.X)
        
        # 替换框
        ttk.Label(main_frame, text="替换为:").pack(pady=(5,2))
        self.replace_entry = ttk.Entry(main_frame)
        self.replace_entry.pack(fill=tk.X)
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        # 按钮
        ttk.Button(btn_frame, text="查找下一个", 
                  command=self.find_next, width=12).pack(side=tk.LEFT, expand=True, padx=2)
        ttk.Button(btn_frame, text="替换", 
                  command=self.replace_text, width=8).pack(side=tk.LEFT, expand=True, padx=2)
        ttk.Button(btn_frame, text="全部替换", 
                  command=self.replace_all, width=12).pack(side=tk.LEFT, expand=True, padx=2)
        
        # 状态标签
        self.search_status = ttk.Label(main_frame, text="")
        self.search_status.pack(pady=2)
        
        # 绑定搜索框变化事件 - 使用定时器实现实时更新
        self.search_after_id = None
        def on_search_change(event=None):
            if self.search_after_id and self.search_window is not None:
                self.search_window.after_cancel(self.search_after_id)
            if self.search_window is not None:
                self.search_after_id = self.search_window.after(300, self.highlight_matches)
        
        self.search_entry.bind('<KeyRelease>', on_search_change)
        
        # 绑定回车键
        self.search_entry.bind('<Return>', lambda e: self.find_next())
        
        # 关闭窗口处理
        self.search_window.protocol("WM_DELETE_WINDOW", self.on_search_window_close)
        self.search_entry.focus_set()

    def highlight_matches(self, event=None):
        """高亮所有匹配项"""
        try:
            # 确保搜索窗口和输入框存在
            if not hasattr(self, 'search_entry') or not self.search_entry:
                return
                
            search_text = self.search_entry.get()  # 使用 search_entry
            
            # 清除之前的高亮和搜索结果
            self.text_area.tag_remove("search", "1.0", tk.END)
            self.text_area.tag_remove("current_match", "1.0", tk.END)
            self.result_list.config(state="normal")  # 允许修改以清空内容
            self.result_list.delete("1.0", tk.END)
            self.matches.clear()
            self.current_match = 0
            
            if not search_text:
                self.search_status.config(text="")
                self.result_list.config(state=tk.DISABLED)  # 禁用输入
                return
                
            content = self.text_area.get("1.0", tk.END)
            
            # 获取所有匹配位置
            start = "1.0"
            while True:
                start = self.text_area.search(search_text, start, tk.END, exact=True)
                if not start:
                    break
                end = f"{start}+{len(search_text)}c"
                self.matches.append((start, end))
                
                # 高亮主文本区域
                self.text_area.tag_add("search", start, end)
                start = end  # 移动到下一个匹配位置
            
            # 更新状态
            if self.matches:
                self.current_match = 1
                start, end = self.matches[0]
                self.text_area.tag_remove("search", start, end)
                self.text_area.tag_add("current_match", start, end)
                self.text_area.see(start)
                self.search_status.config(text=f"找到 {len(self.matches)} 处匹配 (1/{len(self.matches)})")
                
                # 添加匹配项到结果列表
                for i, (start, end) in enumerate(self.matches):
                    line_start = self.text_area.index(f"{start} linestart")
                    line_end = self.text_area.index(f"{start} lineend")
                    line_content = self.text_area.get(line_start, line_end).strip()
                    
                    # 添加到结果列表
                    self.result_list.insert(tk.END, f"匹配 {i + 1}: {line_content}\n")
            else:
                self.search_status.config(text="未找到匹配")
                
        except Exception as e:
            print(f"查找错误: {str(e)}")
            if hasattr(self, 'result_list') and self.result_list:
                self.result_list.config(state=tk.DISABLED)  # 禁用输入

    def _add_line_to_results(self, line_num, line_matches):
        """添加一行的搜索结果到列表"""
        if not line_matches:
            return
            
        # 获取完整的行内容
        line_content = self.text_area.get(f"{line_num}.0", f"{line_num}.end")
        display_text = f"第 {line_num} 行: {line_content}\n"
        
        # 插入行内容
        insert_pos = self.result_list.index("end")
        self.result_list.insert("end", display_text)
        
        # 高亮该行中的所有匹配项
        prefix_len = len(f"第 {line_num} 行: ")
        for _, _, match_text in line_matches:
            pos = 0
            while True:
                pos = line_content.find(match_text, pos)
                if pos == -1:
                    break
                    
                # 计算标签位置
                tag_start = f"{insert_pos} + {pos + prefix_len}c"
                tag_end = f"{insert_pos} + {pos + len(match_text) + prefix_len}c"
                self.result_list.tag_add("match", tag_start, tag_end)
                
                pos += len(match_text)

    def find_next(self):
        """查找下一个匹配项"""
        if not self.matches:
            self.highlight_matches()
            return
            
        if self.current_match > 0:
            # 恢复上一个匹配项的普通高亮
            prev_start, prev_end = self.matches[self.current_match - 1]
            self.text_area.tag_remove("current_match", prev_start, prev_end)
            self.text_area.tag_add("search", prev_start, prev_end)
        
        # 更新当前匹配项
        self.current_match = (self.current_match % len(self.matches)) + 1
        start, end = self.matches[self.current_match - 1]
        
        # 高亮当前匹配项
        self.text_area.tag_remove("search", start, end)
        self.text_area.tag_add("current_match", start, end)
        self.text_area.see(start)
        
        # 更新状态
        self.search_status.config(text=f"找到 {len(self.matches)} 处匹配 ({self.current_match}/{len(self.matches)})")

    def replace_text(self):
        """替换当前匹配项"""
        if not self.matches or self.current_match == 0:
            messagebox.showwarning("警告", "没有找到匹配项！")
            return
            
        replace_text = self.replace_entry.get()
        start, end = self.matches[self.current_match - 1]
        
        # 执行替换
        self.text_area.delete(start, end)
        self.text_area.insert(start, replace_text)
        
        # 重新搜索以更新匹配项
        self.highlight_matches()

    def replace_all(self):
        """替换所有匹配项"""
        if not self.matches:
            messagebox.showwarning("警告", "没有找到匹配项！")
            return
            
        replace_text = self.replace_entry.get()
        count = len(self.matches)
        
        # 从后向前替换，避免位置改变影响替换
        for start, end in reversed(self.matches):
            self.text_area.delete(start, end)
            self.text_area.insert(start, replace_text)
        
        messagebox.showinfo("完成", f"已替换 {count} 处匹配")
        self.highlight_matches()

    def on_search_window_close(self):
        """处理搜索窗口关闭事件"""
        self.text_area.tag_remove("search", "1.0", tk.END)
        self.text_area.tag_remove("current_match", "1.0", tk.END)
        if self.search_window:  # 确保搜索窗口存在
            self.search_window.destroy()
            self.search_window = None

    def update_status(self, event=None):
        # 更新状态栏信息
        content = self.text_area.get(1.0, tk.END)
        chars = len(content) - 1  # 减去最后的换行符
        
        # 获取选中的文本长度
        try:
            sel_start = self.text_area.index(tk.SEL_FIRST)
            sel_end = self.text_area.index(tk.SEL_LAST)
            sel_chars = len(self.text_area.get(sel_start, sel_end))
            status_text = f"总字数: {chars} | 选中: {sel_chars} 个字符"
        except tk.TclError:  # 没有选中文本
            status_text = f"总字数: {chars}"
        
        self.status_bar.config(text=status_text)

    def on_result_select(self, event):
        """处理搜索结果列表选择事件"""
        # 获取光标位置
        try:
            index = self.result_list.index(tk.INSERT)  # 获取光标位置
            line_num = int(index.split('.')[0])  # 获取行号
            
            if 0 <= line_num - 1 < len(self.matches):
                # 更新文本区域的高亮
                start, end = self.matches[line_num - 1]
                self.text_area.tag_remove("current_match", "1.0", tk.END)
                self.text_area.tag_remove("search", start, end)
                self.text_area.tag_add("current_match", start, end)
                self.text_area.see(start)
                self.text_area.mark_set(tk.INSERT, end)
                
                # 更新当前匹配索引
                self.current_match = line_num
                self.search_status.config(text=f"找到 {len(self.matches)} 处匹配 ({self.current_match}/{len(self.matches)})")
        except Exception as e:
            print(f"选择事件处理错误: {str(e)}")
    def refresh_file(self):
        """刷新文件内容"""
        try:
            if self.current_file:
                # 保存当前光标位置
                current_pos = self.text_area.index(tk.INSERT)
                
                # 重新加载文件
                self.load_file(self.current_file)
                
                # 恢复光标位置
                self.text_area.mark_set(tk.INSERT, current_pos)
                self.text_area.see(current_pos)
                
                # 更新状态
                self.status_bar.config(text="文件已刷新")
                
                # 如果有搜索窗口打开，重新执行搜索
                if self.search_window:
                    self.show_search_window()
        except Exception as e:
            messagebox.showerror("错误", f"刷新失败：{str(e)}")

    def on_result_click(self, event):
        """处理搜索结果点击事件"""
        try:
            # 获取点击位置的行号
            index = self.result_list.index(f"@{event.x},{event.y}")
            clicked_line = self.result_list.get(f"{index} linestart", f"{index} lineend")
            
            # 确保 clicked_line 不为空
            if not clicked_line.strip():
                return
            
            # 提取行号 - 改进解析方式以处理不同格式
            try:
                # 尝试提取"匹配 N:"格式的数字
                match_parts = clicked_line.split()
                if len(match_parts) >= 2 and match_parts[0] == "匹配":
                    # 去掉可能的冒号或其他非数字字符
                    match_num_str = ''.join(c for c in match_parts[1] if c.isdigit())
                    line_num = int(match_num_str) if match_num_str else 0
                else:
                    # 尝试提取"第 N 行:"格式的数字
                    for i, part in enumerate(match_parts):
                        if part == "行:" and i > 0 and match_parts[i-1].isdigit():
                            line_num = int(match_parts[i-1])
                            break
                    else:
                        # 如果都失败，则搜索任何数字作为行号
                        digits = re.findall(r'\d+', clicked_line)
                        line_num = int(digits[0]) if digits else 0
            except (ValueError, IndexError):
                # 如果解析失败，则返回
                print(f"无法解析行号，点击的文本: '{clicked_line}'")
                return
            
            # 如果行号为0，则可能解析失败
            if line_num == 0:
                print(f"行号解析结果为0，点击的文本: '{clicked_line}'")
                return
                
            # 行号从1开始，但数组索引从0开始，所以要减1
            match_index = line_num - 1
            
            # 确保索引有效
            if 0 <= match_index < len(self.matches):
                start, end = self.matches[match_index]
                # 验证匹配项是否仍然存在
                try:
                    match_text = self.text_area.get(start, end)
                    if match_text.strip():  # 确保不是空白文本
                        # 更新高亮和光标位置
                        self.text_area.tag_remove("current_match", "1.0", tk.END)
                        self.text_area.tag_remove("search", start, end)
                        self.text_area.tag_add("current_match", start, end)
                        self.text_area.see(start)
                        self.text_area.mark_set(tk.INSERT, end)
                        
                        # 更新当前匹配索引
                        self.current_match = match_index + 1
                        if hasattr(self, 'search_status') and self.search_status:
                            self.search_status.config(text=f"找到 {len(self.matches)} 处匹配 ({self.current_match}/{len(self.matches)})")
                    else:
                        self._safe_highlight_matches()  # 重新搜索
                except:
                    self._safe_highlight_matches()  # 如果获取文本失败，重新搜索
            else:
                self._safe_highlight_matches()  # 如果没有找到匹配项，重新搜索
                
        except Exception as e:
            print(f"处理点击事件错误: {str(e)}")
            self._safe_highlight_matches()  # 发生任何错误时重新搜索

    def _safe_highlight_matches(self):
        """安全地调用highlight_matches方法，避免空引用错误"""
        try:
            if hasattr(self, 'search_entry') and self.search_entry and hasattr(self, 'search_window') and self.search_window:
                self.highlight_matches()
        except Exception as e:
            print(f"安全高亮错误: {str(e)}")

    def _on_text_modified(self, event=None):
        """处理文本修改事件"""
        if not self.text_modified_pending:
            self.text_modified_pending = True
            self.window.after(500, self._handle_text_modified)
        self.text_area.edit_modified(False)

    def _handle_text_modified(self):
        """处理文本修改后的更新"""
        self.text_modified_pending = False
        if hasattr(self, 'search_window') and self.search_window:
                self.highlight_matches()

# 在文件末尾添加以下代码以启动应用程序
# 通过封装为 run() 函数，编译为 .pyd 后可在外部通过 `import main; main.run()` 直接启动。
def run():
    """外部调用主程序入口"""
    app = TextSelector()
    app.window.mainloop()

if __name__ == "__main__":
    run()


